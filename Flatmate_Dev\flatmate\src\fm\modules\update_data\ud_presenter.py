#!/usr/bin/env python3
"""
Update Data presenter implementation.
Coordinates between the view and the data processing pipeline.
"""

import os
from pathlib import Path
from dataclasses import dataclass, field
from typing import List, Optional
import pandas as pd

from ...core.services.event_bus import Events, global_event_bus
from ...core.services.logger import log 

# Import the data service for database operations
#from ...core.data_services.helpers import CSVToTransactionConverter # ! what is this for!? AI slop

# InfoBarService imported locally to avoid circular imports
# configure_auto_import imported locally to avoid circular imports
from ..base.base_presenter import BasePresenter
from .interface import IUpdateDataView
from .config.ud_config import ud_config
from .config.ud_keys import UpdateDataKeys
from ._view_components.option_types import SaveOptions, SourceOptions
from .services.events import UpdateDataEvents
from .pipeline.dw_director import dw_director

# MIGRATION: Import local event bus for event-driven architecture
from .services.local_event_bus import update_data_local_bus, ViewEvents, EventDataFactory, setup_global_event_bridges



@dataclass
class UpdateDataState:
    """
    Presenter state for Update Data module.

    Following MVP pattern: Presenter owns all state, View is stateless.
    This replaces the archived view_context_manager approach.
    """
    # Source configuration
    source_configured: bool = False
    source_type: str = ""  # "folder", "files", "auto_import"
    source_path: str = ""
    selected_files: List[str] = field(default_factory=list)

    # Destination configuration
    destination_configured: bool = False
    save_option: str = "csv"  # "csv", "master", "archive"
    save_path: str = ""
    update_database: bool = True

    # Processing state
    processing: bool = False
    can_process: bool = False
    process_button_text: str = "Select Files First"

    # UI state
    status_message: str = "Select source files or folder to begin"
    error_message: str = ""

    # Auto-import state
    auto_import_enabled: bool = False
    auto_import_pending_count: int = 0

    def update_can_process(self) -> None:
        """Update can_process based on current state."""
        self.can_process = (
            self.source_configured and
            self.destination_configured and
            not self.processing and
            len(self.selected_files) > 0
        )

        # Update process button text based on state
        if self.processing:
            self.process_button_text = "Processing..."
        elif not self.source_configured:
            self.process_button_text = "Select Files First"
        elif not self.destination_configured:
            self.process_button_text = "Set Save Location"
        elif self.can_process:
            self.process_button_text = f"Process {len(self.selected_files)} Files"
        else:
            self.process_button_text = "Cannot Process"

    def reset(self) -> None:
        """Reset state to initial values."""
        self.source_configured = False
        self.source_type = ""
        self.source_path = ""
        self.selected_files.clear()
        self.destination_configured = False
        self.processing = False
        self.error_message = ""
        self.status_message = "Select source files or folder to begin"
        self.update_can_process()


class UpdateDataPresenter(BasePresenter):
    """Presenter for the Update Data module."""


    def __init__(self, main_window, gui_config=None, gui_keys=None):
        """Initialize the Update Data presenter.

        Args:
            main_window: The main window instance
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Call parent constructor
        super().__init__(main_window, gui_config, gui_keys)

        # Initialize presenter state (MVP pattern: presenter owns state)
        self.state = UpdateDataState()

        # The view is type-hinted against the interface, not the implementation.
        self.view: IUpdateDataView

        # Get the InfoBarService instance (import locally to avoid circular imports)
        from ...gui.services.info_bar_service import InfoBarService
        self.info_bar_service = InfoBarService.get_instance()

        # MIGRATION: Add local event bus for event-driven architecture
        self.local_bus = update_data_local_bus

        # State tracking (legacy - will be migrated to ui_state)
        self.selected_source = None  # Dict containing source files/folder info
        self.save_location = None  # Selected save directory path
        self.job_sheet_dict = {}  # Current job sheet dictionary
        self._updating_source_option = False  # Flag to prevent signal loops

        # View manager for morphic UI
        # self.view_manager = UpdateDataViewManager()  # Missing - in archive

        # SimpleStateCoordinator will be initialized in _connect_signals after view creation
        self.state_coordinator = None

    def _create_view(self) -> IUpdateDataView:
        """Create the view instance. Called once during setup."""
        # Import the concrete implementation locally to avoid circular dependencies
        from .ud_view import UpdateDataView

        # Return the concrete view, but the presenter will only interact with it
        # through the IUpdateDataView interface.
        return UpdateDataView(self.main_window, gui_config=self.gui_config, gui_keys=self.gui_keys)

    def _connect_signals(self):
        """Connect view interface signals to handlers. Called once during setup."""
        # Connect to interface signals (high-level domain events)
        self.view.cancel_clicked.connect(lambda: self.request_transition("home"))
        self.view.source_select_requested.connect(self._handle_source_select)
        self.view.save_select_requested.connect(self._handle_save_select)
        self.view.source_option_changed.connect(self._handle_source_option_change)
        self.view.save_option_changed.connect(self._handle_save_option_change)
        self.view.process_clicked.connect(self._handle_process)
        self.view.update_database_changed.connect(self._handle_update_database_change)

        # Connect guide pane signals for monitor folder checkbox
        if hasattr(self.view, 'guide_pane'):
            self.view.guide_pane.message_changed.connect(self._handle_guide_pane_message)

        # Initialize save select button state based on default save option
        initial_save_option = self.view.get_save_option()
        is_same_as_source = initial_save_option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

       
        self.state_coordinator = None  # 
        setup_global_event_bridges()

        log.debug("Signals connected and event bridges set up")

        # Subscribe to Update Data events
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name,
            self._on_processing_started,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name,
            self._on_processing_stats,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.subscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

    def _sync_state_to_view(self) -> None:
        """
        Sync presenter state to view.

        MVP pattern: Presenter state is source of truth, view reflects state.
        This replaces the archived view_context_manager approach.
        """
        # Update process button
        self.view.set_process_button_text(self.state.process_button_text)
        self.view.set_process_enabled(self.state.can_process)

        # Update save controls
        self.view.set_save_select_enabled(self.state.destination_configured)

        # Update source display
        if self.state.source_configured:
            # Use the method to set source option in the left panel
            self.view.left_panel_manager.buttons_widget.set_source_option(f"{self.state.source_type}: {self.state.source_path}")

        # Update save path display
        if self.state.save_path:
            self.view.set_save_path(self.state.save_path)

        # Show error if present
        if self.state.error_message:
            self.view.show_error(self.state.error_message)

        # Update info bar with status
        self.info_bar_service.publish_message(self.state.status_message)

        # Update guide pane with contextual message
        self._update_guide_pane()

    def _update_guide_pane(self):
        """Update guide pane with contextual message based on current state."""
        if not hasattr(self.view, 'guide_pane'):
            return

        # Determine the appropriate message based on state
        if self.state.processing:
            # Show processing message if available
            if hasattr(self.state, 'current_file_index') and hasattr(self.state, 'total_files'):
                self.view.guide_pane.set_state('processing', {
                    'current': self.state.current_file_index,
                    'total': self.state.total_files
                })
            else:
                self.view.guide_pane.display("Processing files...", 'processing')
        elif self.state.can_process:
            # Ready to process - show monitor folder option
            self.view.guide_pane.set_state('ready', {'count': len(self.state.selected_files)})
            # Add monitor folder checkbox as per USER_FLOW_v3.md
            self.view.guide_pane.add_checkbox_option(
                "Monitor this folder for new files",
                checked=False,
                key="monitor_folder"
            )
        elif self.state.source_configured and self.state.destination_configured:
            # Both configured but can't process (no files?)
            if len(self.state.selected_files) == 0:
                self.view.guide_pane.set_state('warning')
                # Still show monitor folder option even if no files currently
                self.view.guide_pane.add_checkbox_option(
                    "Monitor this folder for new files",
                    checked=False,
                    key="monitor_folder"
                )
            else:
                self.view.guide_pane.set_state('ready', {'count': len(self.state.selected_files)})
                # Add monitor folder checkbox
                self.view.guide_pane.add_checkbox_option(
                    "Monitor this folder for new files",
                    checked=False,
                    key="monitor_folder"
                )
        elif self.state.source_configured:
            # Source configured, show file count
            if self.state.source_type == "folder":
                self.view.guide_pane.set_state('folder_selected', {'count': len(self.state.selected_files)})
            else:
                self.view.guide_pane.set_state('files_selected', {'count': len(self.state.selected_files)})
        else:
            # Initial state
            self.view.guide_pane.set_state('initial')

    def _refresh_content(self, **params):
        """Refresh update data content when shown.

        This method is called every time the module becomes visible.
        It handles view state setup and configuration.

        Args:
            **params: Optional parameters passed from navigation
        """
        log.debug("Refreshing UpdateData content")

        # Set the source option from config to remember user's last choice
        last_source_option = ud_config.get_value(
            UpdateDataKeys.Source.LAST_SOURCE_OPTION, default=SourceOptions.SELECT_FOLDER.value
        )
        self.view.set_source_option(last_source_option)

        # Configure view based on database mode using interface methods
        is_database_mode = self.view.get_update_database()

        # Set initial process button state
        self.view.set_process_button_text(self.state.process_button_text)

        # Explicitly control panel visibility - presenter is responsible for UI state
        self.main_window.show_left_panel()

        # Show the InfoBar with appropriate message
        self.info_bar_service.show()
        self.info_bar_service.publish_message(
            "Select source files or folder to begin.", "INFO"
        )

        self._setup_view_from_config()

        log.debug("UpdateData content refresh complete")



    def _handle_source_select(self, selection_type: str):
        """Handle source selection request."""
        log.debug(f"Source selection requested for type: {selection_type}")
        initial_dir = str(
            ud_config.get_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, default=str(Path.home()))
        )

        if selection_type == SourceOptions.SELECT_FOLDER.value:
            folder = self.view.show_folder_dialog(
                "Select Source Folder", initial_dir=initial_dir
            )
            if not folder:
                return

            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, folder)

            # Get all supported files from the folder
            extensions = ud_config.get_allowed_file_extensions()
            file_paths = [
                str(p)
                for p in Path(folder).iterdir()
                if p.is_file() and p.suffix.lower() in extensions
            ]

            if not file_paths:
                ext_list = ", ".join(extensions)
                self.view.show_error(f"No supported files found in selected folder. Looking for: {ext_list}")
                return

            # Create a dictionary with source information
            self.selected_source = {
                "type": "folder",
                "path": folder,
                "file_paths": file_paths,
            }

            # Update state
            self.state.source_configured = True
            self.state.source_type = "folder"
            self.state.source_path = folder
            self.state.selected_files = file_paths
            self.state.update_can_process()
            self._sync_state_to_view()

        elif selection_type == SourceOptions.SELECT_FILES.value:
            files = self.view.show_files_dialog(
                "Select Source Files", initial_dir=initial_dir
            )
            if not files:
                return

            # Save the directory of the first file for next time
            last_dir = os.path.dirname(files[0])
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SOURCE_DIR, last_dir)

            # Create a dictionary with source information
            self.selected_source = {"type": "files", "file_paths": files}

            # Update state
            self.state.source_configured = True
            self.state.source_type = "files"
            self.state.source_path = last_dir
            self.state.selected_files = files
            self.state.update_can_process()
            self._sync_state_to_view()

        else:
            self.view.show_error(f"Unknown source selection type: {selection_type}")
            return

        # MIGRATION: Emit events instead of direct view manipulation
        if self.selected_source:
            # Emit source discovered event for state coordinator
            source_data = EventDataFactory.source_discovered(
                source_type=self.selected_source["type"],
                files=self.selected_source["file_paths"],
                path=self.selected_source.get("path", ""),
                count=len(self.selected_source["file_paths"])
            )
            self.local_bus.emit(ViewEvents.SOURCE_DISCOVERED.value, source_data)

            # Emit files display update event for view
            files_display_data = EventDataFactory.files_display_update(
                files=self.selected_source["file_paths"],
                source_path=self.selected_source.get("path", os.path.dirname(self.selected_source["file_paths"][0]) if self.selected_source["file_paths"] else "")
            )
            self.local_bus.emit(ViewEvents.FILES_DISPLAY_UPDATED.value, files_display_data)

            # MIGRATION NOTE: Removed direct view calls:
            # - self.view.display_selected_source(self.selected_source)
            # - self.view.set_process_mode()
            # - Direct state_coordinator calls
            # These are now handled via events



            # If save option is "Same as source", update the save location now
            if self.view.get_save_option() == SaveOptions.SAME_AS_SOURCE.value:
                self._handle_save_option_change(SaveOptions.SAME_AS_SOURCE.value)

    def _handle_save_select(self):
        """Handle save location selection."""
        folder = self.view.show_folder_dialog(
            "Select Save Location",
            initial_dir=str(ud_config.get_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, default=str(Path.home()))),
        )
        if folder:
            self.save_location = folder
            ud_config.set_value(UpdateDataKeys.Paths.LAST_SAVE_DIR, folder)
            self.view.set_save_path(folder)

            # Notify SimpleStateCoordinator about custom destination
            if self.state_coordinator:
                self.state_coordinator.set_destination_custom(folder)

            # Update state
            self.state.destination_configured = True
            self.state.save_path = folder
            self.state.update_can_process()
            self._sync_state_to_view()

            self.info_bar_service.publish_message(f"Save location: {folder}")

    def _handle_guide_pane_message(self, message: str):
        """Handle guide pane messages including monitor folder checkbox changes."""
        if message.startswith("checkbox_changed:monitor_folder:"):
            # Extract the checkbox state (0 = unchecked, 2 = checked)
            state = message.split(":")[-1]
            is_checked = state == "2"

            # Update the monitor folder setting
            self._handle_monitor_folder_change(is_checked)

            log.debug(f"Monitor folder checkbox changed: {is_checked}")

    def _handle_monitor_folder_change(self, enabled: bool):
        """Handle monitor folder setting change."""
        # Store the monitor folder preference
        if hasattr(self, 'selected_source') and self.selected_source:
            self.selected_source['monitor_folder'] = enabled

            if enabled:
                self.info_bar_service.publish_message("Folder monitoring enabled - new files will be detected automatically")
            else:
                self.info_bar_service.publish_message("Folder monitoring disabled")

    def _handle_source_option_change(self, option: str):
        """Handle source option change."""
        # Skip processing if we're programmatically updating the option
        if self._updating_source_option:
            return

        # Reset selected source when option changes
        self.selected_source = None
        self.view.display_selected_source(None)
        self.info_bar_service.clear()
        self.info_bar_service.publish_message(f"Selected source option: {option}")

        # Save the selected option to config to remember the choice
        ud_config.set_value(UpdateDataKeys.Source.LAST_SOURCE_OPTION, option)

    def _handle_save_option_change(self, option: str):
        """Handle save location option change."""
        # Disable save select button when using same as source
        is_same_as_source = option == SaveOptions.SAME_AS_SOURCE.value
        self.view.set_save_select_enabled(not is_same_as_source)

        if is_same_as_source:
            # Update the save location label to indicate "Same as source..."
            self.view.set_save_path("Same as source...")

            if self.selected_source:
                # If we have a selected source, use its directory
                if self.selected_source["type"] == "folder":
                    self.save_location = self.selected_source["path"]
                else:  # files
                    self.save_location = os.path.dirname(
                        self.selected_source["file_paths"][0]
                    )
                self.info_bar_service.publish_message(
                    f"Save location: {self.save_location}"
                )

                # Notify SimpleStateCoordinator about destination selection
                if self.state_coordinator:
                    self.state_coordinator.set_destination_same_as_source()

                # Update state
                self.state.destination_configured = True
                self.state.save_path = self.save_location
                self.state.update_can_process()
                self._sync_state_to_view()
        else:
            # Reset save location if not using same as source
            self.save_location = None
            self.view.set_save_path("")  # Clear the save path label
            self.info_bar_service.publish_message("")

            # Update state
            self.state.destination_configured = False
            self.state.save_path = ""
            self.state.update_can_process()
            self._sync_state_to_view()

    def _handle_update_database_change(self, checked: bool):
        """Handle database update checkbox state change - drives UI morphing."""
        # Store the state in configuration
        ud_config.set_value(UpdateDataKeys.Database.UPDATE_DATABASE, checked)

        # Configure UI based on mode using view manager, but preserve checkbox state
        self.view_manager.configure_view_for_workflow_preserve_checkbox(
            self.view,
            is_database_mode=checked,
            preserve_checkbox_state=True
        )

        # Update button text based on mode
        if checked:
            self.view.left_panel_manager.buttons_widget.process_btn.setText("Update Database")
            status_msg = "Database mode: Files will be imported to database"
        else:
            self.view.left_panel_manager.buttons_widget.process_btn.setText("Process Files")
            status_msg = "File utility mode: Files will be processed without database updates"

        self.info_bar_service.publish_message(status_msg)

    def _handle_process(self):
        """
        Handle process button click.

        MIGRATION: Now emits events instead of direct state coordinator calls.
        """
        if not self.selected_source:
            # MIGRATION: Emit error event instead of direct view call
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              EventDataFactory.dialog_request("error", "Error", "No source files selected."))
            return

        if not self.save_location:
            # MIGRATION: Emit error event instead of direct view call
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              EventDataFactory.dialog_request("error", "Error", "No save location selected."))
            return

        # Create job sheet
        self.job_sheet_dict = {
            "filepaths": self.selected_source["file_paths"],
            "save_folder": self.save_location,
            "update_database": self.view.get_update_database(),
        }

        # MIGRATION: Emit processing started event instead of direct state coordinator call
        processing_data = EventDataFactory.processing_started(self.job_sheet_dict)
        self.local_bus.emit(ViewEvents.PROCESSING_STARTED.value, processing_data)

        # Log the job sheet for debugging
        log.debug(f"Processing job sheet: {self.job_sheet_dict}")

        try:
            # Use the director function to process the files
            result = dw_director(self.job_sheet_dict)

            # Check the result from the director
            if result.get("status") == "success":
                # MIGRATION: Emit processing completed event instead of direct calls
                processed_count = len(self.job_sheet_dict.get("filepaths", []))
                completion_data = EventDataFactory.processing_completed(
                    success=True,
                    result={
                        "processed_count": processed_count,
                        "message": result.get("message", "Processing complete!")
                    }
                )
                self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, completion_data)

                # MIGRATION: Emit success dialog event instead of direct view call
                self.local_bus.emit(ViewEvents.SUCCESS_DIALOG_REQUESTED.value,
                                  EventDataFactory.dialog_request("success", "Success",
                                                                 result.get("message", "Processing complete!")))
            else:
                error_msg = result.get("message", "An unknown error occurred.")
                log(f"Processing error from director: {result}", "e")

                # MIGRATION: Emit processing failed and error dialog events
                failure_data = EventDataFactory.processing_completed(
                    success=False,
                    result={"error": error_msg}
                )
                self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, failure_data)
                self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                                  EventDataFactory.dialog_request("error", "Error", error_msg))

        except Exception as e:
            log.error(f"Error during processing: {e}", "e")

            # MIGRATION: Emit processing failed and error dialog events
            failure_data = EventDataFactory.processing_completed(
                success=False,
                result={"error": str(e)}
            )
            self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, failure_data)
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              EventDataFactory.dialog_request("error", "Error", f"An error occurred: {e}"))

        # MIGRATION: Reset state via events instead of direct view calls
        self.selected_source = None
        # TODO: Add reset/cleanup events if needed
        # For now, the state coordinator will handle UI reset via processing completion events

    def request_transition(self, target_view: str):
        """Request transition to another view."""
        global_event_bus.publish(Events.REQUEST_VIEW_TRANSITION.name, target_view)

    def cleanup(self):
        """Clean up before being replaced."""
        # Unsubscribe from events
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STARTED.name, self._on_processing_started
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_STATS.name, self._on_processing_stats
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.UNRECOGNIZED_FILES_DETECTED.name,
            self._on_unrecognized_files,
        )
        global_event_bus.unsubscribe(
            UpdateDataEvents.FILE_PROCESSING_COMPLETED.name,
            self._on_processing_completed,
        )

        self.info_bar_service.hide()

        if self.view:
            self.view.cleanup()


    def _setup_view_from_config(self):
        """Set up view based on configuration."""
        # Set default save location
        self.save_location = ud_config.get_value("master")
        self.info_bar_service.publish_message(f"Save location: {self.save_location}")

        # Load recent masters
        recent_masters = ud_config.get_pref("recent_masters", default=[])
        if recent_masters:
            # TODO: Add recent masters to view
            pass

    def _on_processing_started(self, job_sheet):
        """Handle processing started event."""
        file_count = len(job_sheet.get("filepaths", [])) or 0
        log.info(f"Processing started for {file_count} files")

        # Update both the InfoBar and the info widget for consistency
        self.info_bar_service.show()
        self.info_bar_service.publish_message(f"Processing {file_count} files...")

    def _on_processing_stats(self, stats):
        """Handle processing stats event."""
        log.debug(f"Processing stats: {stats}")
        total = stats.get("total_files", 0)
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Update progress in the info widget
        self.info_bar_service.publish_message(f"Processing: {processed}/{total} files")

        # Update the InfoBar with current progress
        if total > 0:
            self.info_bar_service.publish_message(
                f"Processing files: {processed}/{total} complete"
            )

    def _on_unrecognized_files(self, unrecognized_files):
        """Handle unrecognized files event."""
        log.warning(f"Unrecognized files detected: {unrecognized_files}")

        # Update the InfoBar with a warning about unrecognized files
        count = len(unrecognized_files)
        self.info_bar_service.publish_message(
            f"Warning: {count} unrecognized file(s) detected"
        )

        # Also update the info widget with detailed error messages
        for file_info in unrecognized_files:
            self.info_bar_service.publish_message(
                f"Warning: Unrecognized file: {file_info['filepath']}\n"
                f"Reason: {file_info.get('reason', 'Unknown')}"
            )

    def _on_processing_completed(self, result):
        """Handle processing completed event."""
        log.info("File processing completed")
        stats = result.get("stats", {})
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Get backup stats if available
        backup_stats = result.get("backup_stats", {})
        backed_up = backup_stats.get("backed_up_count", 0)
        skipped = backup_stats.get("skipped_count", 0)

        # Update the InfoBar with completion message
        status_msg = f"Processing complete. {processed} files processed successfully."
        self.info_bar_service.publish_message(status_msg)

        # Build status message with all stats
        status_msg = f"Processing complete. {processed} files processed successfully."

        # Add backup stats if available
        if backed_up > 0 or skipped > 0:
            status_msg += (
                f" {backed_up} files backed up, {skipped} identical files skipped."
            )

        # Add error info if unrecognized files exist
        if unrecognized > 0:
            status_msg += f" {unrecognized} files unrecognized."
            self.info_bar_service.publish_message(f"Error: {status_msg}")
        else:
            self.info_bar_service.publish_message(status_msg)
