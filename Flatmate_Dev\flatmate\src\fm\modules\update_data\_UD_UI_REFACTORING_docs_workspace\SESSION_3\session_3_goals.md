# Session 3 Goals - User Experience Implementation
**Date**: 2025-07-28  
**Priority**: 🔥 HIGH - User experience is broken  
**Timeline**: Target completion in 1-2 focused sessions

## 🎯 Primary Objective

**Build the UI specified in `_USER_FLOW_v3.md`** - the 3-step workflow that users actually need:

1. **Source Selection**: Select folder or files
2. **Archive Location**: Same as source or custom location  
3. **Process**: <PERSON><PERSON> becomes active, processes files

## 🚨 Current Problems (User Feedback)

### **Left Panel Issues**
- "Fake options in source options" 
- No clear Source Files section
- No Archive section
- Process button not properly managed

### **Center Panel Issues**  
- "One giant pane with zero information"
- No file display when source selected
- Guide pane not showing contextual messages

### **Overall Flow Issues**
- 3-step workflow completely broken
- No state transitions between steps
- No user feedback or guidance

## 📋 Detailed Goals

### **Goal 1: Fix Left Panel Structure**
**Current State**: Broken source options, no archive section  
**Target State**: Clean Source Files + Archive sections per spec

#### **Source Files Section**
```
Source Files
├── [Select Folder] button
├── [Select Files] button  
└── Selected path display
```

#### **Archive Section** (inactive until source selected)
```
Archive
├── ( ) Same as source
├── ( ) Custom location
└── [Select Folder] button (if custom selected)
```

#### **Process Section**
```
[PROCESS FILES] button
├── Inactive state: "Select Files First" (gray)
├── Ready state: "Process X Files" (active)
└── Processing state: "Processing..." (animated)
```

### **Goal 2: Implement Guide Pane Messaging**
**Current State**: Static or empty guide pane  
**Target State**: Contextual messages per user flow

#### **Message Flow**
1. **Initial**: "Select a source folder or files to begin"
2. **Source Selected**: "Found [X] CSV files ready for processing"  
3. **Archive Configured**: "Ready to process [X] files"
4. **Processing**: "Processing file 3 of 15..."
5. **Complete**: "Successfully processed [X] files"

### **Goal 3: Center Panel File Display**
**Current State**: Empty pane with no information  
**Target State**: File listing when source selected

#### **File Display Features**
- List of selected/discovered files
- File count and types
- File validation status
- Clear visual feedback

### **Goal 4: State Management Integration**
**Current State**: `UpdateDataState` exists but not connected to UI  
**Target State**: State drives all UI updates

#### **State → UI Mapping**
```python
# State changes trigger UI updates:
state.source_configured → Enable archive section
state.destination_configured → Enable process button  
state.selected_files → Update file display
state.process_button_text → Update button text
state.status_message → Update guide pane
```

## 🔗 Reference Documents

### **Primary Specification**
- **`_USER_FLOW_v3.md`** - THE source of truth for what we're building
- **`user_journey_flow_v2.md`** - Additional user experience details

### **Technical Foundation (Session 2)**
- **`UpdateDataState` dataclass** - State management system
- **`IUpdateDataView` interface** - View contract
- **`session_2_wrap_up.md`** - What's already working

### **Component Analysis**
- **`center_panel_widgets_analysis_250728.md`** - Available widgets
- **`next_steps_and_priorities_250728.md`** - Implementation priorities

## 🎯 Success Metrics

### **User Validation Criteria**
1. **Intuitive Flow**: User can complete workflow without confusion
2. **Clear Feedback**: User always knows what to do next
3. **Proper States**: UI elements enable/disable appropriately
4. **File Display**: User can see what files will be processed
5. **Process Works**: User can successfully process files

### **Technical Validation Criteria**
1. **State Driven**: All UI changes driven by `UpdateDataState`
2. **Interface Compliant**: View implements all interface methods properly
3. **Signal Connected**: User actions trigger presenter methods
4. **Error Handling**: Graceful handling of invalid selections
5. **Performance**: Responsive UI during file discovery/processing

## 🚀 Implementation Strategy

### **Phase 1: Left Panel Structure (Priority 1)**
- Fix source selection section
- Add archive selection section
- Connect to state management
- Enable/disable based on state

### **Phase 2: Guide Pane Messaging (Priority 1)**  
- Implement contextual messaging
- Connect to state changes
- Test message flow

### **Phase 3: File Display (Priority 2)**
- Show selected files in center panel
- File count and validation
- Clear visual feedback

### **Phase 4: Process Integration (Priority 2)**
- Connect process button to actual processing
- Progress feedback
- Results display

### **Phase 5: Polish and Testing (Priority 3)**
- Error handling
- Edge cases
- User testing and refinement

## ⚠️ What NOT to Do

1. **Don't Add More Architecture** - Use what Session 2 provided
2. **Don't Over-Engineer** - Build the minimum viable UX first
3. **Don't Ignore the Spec** - `_USER_FLOW_v3.md` is the requirement
4. **Don't Skip User Testing** - Validate each step works as expected

## 📊 Definition of Done

**Session 3 is complete when:**
- User can select source (folder or files)
- User can choose archive location (same or custom)
- Process button activates when both configured
- Guide pane shows helpful contextual messages
- File display shows what will be processed
- Basic processing workflow completes successfully

**User feedback changes from:**
- ❌ "Fake options, giant empty pane, zero information"
- ✅ "Clear workflow, helpful guidance, shows my files"

---
*Session 3: Finally building the UI that users actually need*
