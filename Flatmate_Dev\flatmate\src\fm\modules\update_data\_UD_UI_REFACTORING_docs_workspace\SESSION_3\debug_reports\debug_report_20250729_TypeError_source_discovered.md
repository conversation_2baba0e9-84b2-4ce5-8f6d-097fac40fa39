# Debug Report: TypeError in `source_discovered`

**Report Date:** 2025-07-29
**Status:** Analysis Complete

## 1. Issue Summary

While testing the source folder selection feature, a `TypeError` occurs after a folder is selected. The application fails with the error:

```
TypeError: EventDataFactory.source_discovered() missing 1 required positional argument: 'count'
```

This happens in `ud_presenter.py` when creating the `source_data` event payload.

## 2. Root Cause Analysis

The error is caused by a mismatch between the method signature of `EventDataFactory.source_discovered` and how it's being called.

- **Method Definition (`.../services/local_event_bus.py`):**
  The factory method is defined to accept four arguments:
  `def source_discovered(source_type: str, files: List[str], path: str, count: int):`

- **Method Call (`ud_presenter.py`, line 390):
  The method is called with only three arguments, missing `count`:
  ```python
  source_data = EventDataFactory.source_discovered(
      source_type=self.selected_source["type"],
      files=self.selected_source["file_paths"],
      path=self.selected_source.get("path", "")
  )
  ```

The `count` of discovered files is a required part of the event data structure, but it's not being passed from the presenter.

## 3. Affected Files

1.  `c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py` (Calling code)
2.  `c:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\services\local_event_bus.py` (Method definition)

## 4. Proposed Solutions

### Solution 1 (Recommended): Add `count` to the Method Call

Modify the call in `ud_presenter.py` to include the file count, which can be derived directly from the `file_paths` list.

- **File:** `ud_presenter.py`
- **Change:**
  ```python
  # From
  source_data = EventDataFactory.source_discovered(
      source_type=self.selected_source["type"],
      files=self.selected_source["file_paths"],
      path=self.selected_source.get("path", "")
  )

  # To
  source_data = EventDataFactory.source_discovered(
      source_type=self.selected_source["type"],
      files=self.selected_source["file_paths"],
      path=self.selected_source.get("path", ""),
      count=len(self.selected_source["file_paths"])
  )
  ```

- **Pros:**
  - Simple, direct fix.
  - Correctly uses the existing `EventDataFactory` interface.
  - Ensures the event data payload is complete as designed.
- **Cons:**
  - None.

### Solution 2 (Not Recommended): Make `count` Optional in the Factory

Modify the `EventDataFactory.source_discovered` method to calculate the count itself if it's not provided.

- **File:** `local_event_bus.py`
- **Change:**
  ```python
  # From
  def source_discovered(source_type: str, files: List[str], path: str, count: int):

  # To
  def source_discovered(source_type: str, files: List[str], path: str, count: Optional[int] = None):
      if count is None:
          count = len(files)
  ```

- **Pros:**
  - Might make the factory method slightly more robust to incomplete calls.
- **Cons:**
  - Obscures the data contract. If the event is for `source_discovered`, the number of files is essential information and should be explicitly provided by the caller who did the discovery.
  - It patches the symptom, not the cause, which is an incorrect call in the presenter.

## 5. Recommendation

**Implement Solution 1.** It is the correct and most direct way to fix the bug, adhering to the established data structures and ensuring the presenter provides all necessary information for the event.

Let me know if you'd like me to apply this fix for you.
