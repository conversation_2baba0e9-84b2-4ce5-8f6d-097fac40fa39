# testing ud_data

1. ADRESS THESE ERRORS

Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 45, in <lambda>
    lambda: self.request_transition("update_data")
            ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 121, in transition_to    self.current_module.show(**params)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 118, in show    self._refresh_content(**params)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 306, in _refresh_content
    if is_database_mode and auto_import_status.get('enabled', False):
                            ^^^^^^^^^^^^^^^^^^
NameError: name 'auto_import_status' is not defined
TypeError: source_select_requested() only accepts 0 argument(s), 1 given!
TypeError: source_select_requested() only accepts 0 argument(s), 1 given!


2 ar