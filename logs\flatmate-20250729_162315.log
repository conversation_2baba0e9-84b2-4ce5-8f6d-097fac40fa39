2025-07-29 16:23:15 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-29 16:23:17 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-29 16:23:17 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-29 16:23:17 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-29 16:23:17 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-29 16:23:17 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-07-29 16:23:17 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-07-29 16:23:17 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-29 16:23:17 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-29 16:23:17 - [fm.main] [INFO] - Application starting...
2025-07-29 16:23:20 - [fm.main] [INFO] - 
=== Initializing Database & Cache ===
2025-07-29 16:23:20 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-07-29 16:23:20 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-29 16:23:20 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-07-29 16:23:20 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-07-29 16:23:20 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2099 transactions, 3 unique accounts in 0.60s
2025-07-29 16:23:20 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2099 transactions
2025-07-29 16:23:20 - [fm.main] [INFO] - 
=== Initializing Auto-Import Manager ===
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] - Auto-import paths configured:
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] -   Import: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] -   Archive: C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\fm_auto_imported
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] -   Failed: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] - AutoImportManager initialized - monitoring: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] - Auto-import worker thread started
2025-07-29 16:23:20 - [fm.core.services.auto_import_manager] [INFO] - Auto-import monitoring started for: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 16:23:20 - [fm.main] [INFO] - Auto-import monitoring started
2025-07-29 16:23:20 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-29 16:23:20 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-29 16:23:20 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-29 16:23:20 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-29 16:23:20 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-29 16:23:20 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-29 16:23:20 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-29 16:23:20 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-29 16:23:20 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-29 16:23:21 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected and event bridges set up
2025-07-29 16:23:21 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-29 16:23:21 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-29 16:23:21 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-29 16:23:21 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - Cache hit: returning 3 unique accounts
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-29 16:23:21 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-29 16:23:21 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.268s
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-29 16:23:21 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-07-29 16:23:21 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-29 16:23:21 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-29 16:23:21 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions as DataFrame
2025-07-29 16:23:21 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.041s
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2099, 32), empty: False
2025-07-29 16:23:21 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 16:23:22 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  ...  is_deleted
0  38-9004-0646977-04  ...           0
1  38-9004-0646977-04  ...           0
2  38-9004-0646977-04  ...           0
3  38-9004-0646977-04  ...           0
4  38-9004-0646977-00  ...           0

[5 rows x 32 columns]
2025-07-29 16:23:22 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2099, 32)
2025-07-29 16:23:22 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-07-29 16:23:22 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-29 16:23:22 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.047s
2025-07-29 16:23:22 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-07-29 16:23:22 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-07-29 16:23:22 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.007s
2025-07-29 16:23:22 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-07-29 16:23:22 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-29 16:23:22 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-29 16:23:22 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 16:23:22 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-29 16:23:22 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Using ordered display columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes']
2025-07-29 16:23:22 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Reordered DataFrame columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes', 'credit_amount', 'db_uid', 'debit_amount', 'empty', 'hash', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 16:23:24 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Date', 'Details', 'Amount', 'Account', 'Balance', 'Category', 'Tags', 'Notes']
2025-07-29 16:23:24 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 2.650s
2025-07-29 16:23:24 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 3.0s (710.7 txns/s)
2025-07-29 16:23:24 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 2.956s
2025-07-29 16:23:24 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-07-29 16:23:24 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-29 16:23:24 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-07-29 16:23:24 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-29 16:23:24 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-29 16:23:24 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-29 16:23:24 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-29 16:23:24 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-29 16:23:24 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-07-29 16:23:24 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-29 16:23:24 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-29 16:23:24 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-07-29 16:23:24 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-29 16:23:24 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-29 16:23:24 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-29 16:23:24 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-29 16:23:24 - [fm.main] [INFO] - 
=== Checking Auto-Import Status ===
2025-07-29 16:23:24 - [fm.main] [INFO] - Auto-import startup check temporarily disabled - using new state management
2025-07-29 16:23:24 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-29 16:23:28 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-29 16:23:28 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-29 16:23:28 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-29 16:23:28 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-29 16:23:28 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-29 16:23:28 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-29 16:23:28 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-29 16:23:28 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-29 16:23:28 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-29 16:23:28 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-29 16:23:28 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-29 16:23:34 - [fm.modules.update_data.ud_presenter] [DEBUG] - Source selection requested for type: Select entire folder...
2025-07-29 16:30:12 - [fm.modules.update_data.ud_presenter] [DEBUG] - Source selection requested for type: Select entire folder...
2025-07-29 16:52:05 - [fm.core.services.auto_import_manager] [INFO] - New CSV file detected: 38-9004-0646977-00_29Jul.CSV
2025-07-29 16:52:06 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-00_29Jul.CSV
2025-07-29 16:52:07 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-00_29Jul.CSV
2025-07-29 16:52:07 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-00_29Jul.CSV
2025-07-29 16:52:08 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-00_29Jul.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:08 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-00_29Jul.CSV -> 38-9004-0646977-00_29Jul_20250729_165208_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:10 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-00_29Jul.CSV
2025-07-29 16:52:19 - [fm.core.services.auto_import_manager] [INFO] - New CSV file detected: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:19 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:20 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:21 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-04_29Jul.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:21 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-04_29Jul.CSV -> 38-9004-0646977-04_29Jul_20250729_165221_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:23 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:25 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:27 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:29 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-04_29Jul.CSV
2025-07-29 16:52:50 - [fm.core.services.auto_import_manager] [INFO] - New CSV file detected: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:50 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:51 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:52 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:52 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED.CSV
2025-07-29 16:52:52 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED.CSV
2025-07-29 16:52:52 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:52:54 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:56 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:52:58 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:53:00 - [fm.core.services.auto_import_manager] [WARNING] - File no longer exists: 38-9004-0646977-01_29Jul.CSV
2025-07-29 16:53:02 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:02 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED.CSV
2025-07-29 16:53:02 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED.CSV
2025-07-29 16:53:02 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:04 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:04 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED.CSV
2025-07-29 16:53:04 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED.CSV
2025-07-29 16:53:04 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:06 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:06 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED.CSV
2025-07-29 16:53:06 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED.CSV
2025-07-29 16:53:06 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:08 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:08 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED.CSV
2025-07-29 16:53:08 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED.CSV
2025-07-29 16:53:08 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:10 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:10 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED.CSV
2025-07-29 16:53:10 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED.CSV
2025-07-29 16:53:10 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:12 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:12 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED.CSV
2025-07-29 16:53:12 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED.CSV
2025-07-29 16:53:12 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:14 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:14 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED.CSV
2025-07-29 16:53:14 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED.CSV
2025-07-29 16:53:14 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:16 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:16 - [fm.core.services.auto_import_manager] [DEBUG] - CSV file modified: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV
2025-07-29 16:53:16 - [fm.core.services.auto_import_manager] [DEBUG] - Queued for processing: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV
2025-07-29 16:53:16 - [fm.core.services.auto_import_manager] [ERROR] - Moved to failed: 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED.CSV -> 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV (Error: No module named 'fm.modules.update_data.utils')
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:18 - [fm.core.services.auto_import_manager] [ERROR] - Error processing file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV: No module named 'fm.modules.update_data.utils'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'
2025-07-29 16:53:18 - [fm.core.services.auto_import_manager] [ERROR] - Failed to move failed file 38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV: [WinError 123] The filename, directory name, or volume label syntax is incorrect
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 228, in _process_single_file
    from fm.modules.update_data.utils.dw_director import dw_director
ModuleNotFoundError: No module named 'fm.modules.update_data.utils'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.2\Lib\shutil.py", line 856, in move
    os.rename(src, real_dst)
    ~~~~~~~~~^^^^^^^^^^^^^^^
OSError: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'C:\\Users\\<USER>\\Downloads\\_flatmete_auto_import\\38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED.CSV' -> 'C:\\Users\\<USER>\\Downloads\\_flatmete_auto_import\\38-9004-0646977-01_29Jul_20250729_165252_FAILED_20250729_165302_FAILED_20250729_165304_FAILED_20250729_165306_FAILED_20250729_165308_FAILED_20250729_165310_FAILED_20250729_165312_FAILED_20250729_165314_FAILED_20250729_165316_FAILED_20250729_165318_FAILED.CSV'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\services\auto_import_manager.py", line 272, in _move_to_failed
    shutil.move(str(file_path), str(failed_file))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.2\Lib\shutil.py", line 876, in move
    copy_function(src, real_dst)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.pyenv\pyenv-win\versions\3.13.2\Lib\shutil.py", line 453, in copy2
    _winapi.CopyFile2(src_, dst_, flags)
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
OSError: [WinError 123] The filename, directory name, or volume label syntax is incorrect
2025-07-29 16:54:56 - [fm.modules.update_data.ud_presenter] [DEBUG] - Source selection requested for type: Select entire folder...
