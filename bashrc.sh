#!/usr/bin/env bash


####################################
#        Environment Variables     #
####################################
# Uncomment to enable colors if needed:
# export CLICOLOR=1
# export LS_COLORS='no=00:fi=00:di=01;34:ln=01;36:*.py=01;35:*.exe=01;32:*.sh=01;32:*.bat=01;32:*.cmd=01;32'

# Auto-activate venv when in flatmate directory
# if [[ $PWD == */Flatmate_Dev/flatmate* ]]; then
#     avenv 2>/dev/null
# fi

####################################
#         Theme Management         
####################################
theme() {
    local theme="${1:-}"
    local themes_dir="$HOME/.poshthemes"
    if [ -z "$theme" ]; then
        echo "Available themes:" >&2
        if [ -d "$themes_dir" ]; then
            find "$themes_dir" -maxdepth 1 -name "*.json" -print0 2>/dev/null | \
                xargs -0 -n1 basename -- 2>/dev/null | \
                sed 's/\.omp\.json$//' | \
                sort
        else
            echo "No themes directory found: $themes_dir" >&2
            return 1
        fi
        return 0
    fi
    local theme_file="$themes_dir/${theme}.omp.json"
    if [ -f "$theme_file" ]; then
        if command -v oh-my-posh >/dev/null 2>&1; then
local cmd_output
            cmd_output=$(oh-my-posh init bash --config "$theme_file" 2>/dev/null) || {
                printf '%s\n' "Failed to set theme: $theme" >&2
                return 1
            }
            eval "$cmd_output" || {
                printf '%s\n' "Failed to set theme: $theme" >&2
                return 1
            }
            echo "Theme set to: $theme"
            return 0
        else
            echo "Error: oh-my-posh is not installed" >&2
            return 1
        fi
    else
        echo "Theme not found: $theme" >&2
        theme "" || return 1
        return 1
    fi
}

####################################
#              Aliases            #
####################################

# --- General ---
alias src='source ~/.bashrc'

# Copy bashrc to home directory
alias flashbash='cp ~/_DEV/dev_tools/bash_workbench/bashrc.sh ~/.bashrc'

# Copy and source bashrc
alias flash='flashbash && src && echo "Bashrc has been updated and sourced! 🚀"'
alias gitd='C:/Users/<USER>/AppData/Local/GitHubDesktop/GitHubDesktop.exe'
# Open files in Notepad++ (runs in foreground)
alias np='notepad++'
# Backward compatibility
alias flashbrc='flash'
# Copy zash.omp.json to Oh My Posh themes directory
alias flashzash='cp ~/_DEV/dev_tools/bash_workbench/zash.omp.json ~/.poshthemes/zash.omp.json'
# Confirm bashrc update worked
alias vfbash='echo "flashed bashrc2 is alive! 🦾"'
# List all aliases
list_aliases() {
    alias | sort
}
alias aliases='list_aliases'

# --- Navigation ---
alias ..='cd ..'          # Up one directory
alias ...='cd ../../'    # Up two directories
alias ....='cd ../../../' # Up three directories

# --- Listing ---
alias ls='ls -F --color=auto'
alias ll='ls -l'
alias la='ls -la'

# --- System ---
alias c='clear'
alias admint='wt -p "Windows PowerShell" --title "Admin PowerShell" --startingDirectory ~ runas /user:Administrator'
# Alternative admin PowerShell alias (uncomment if needed):
# alias admint='powershell.exe -NoExit -Command "Start-Process powershell -Verb RunAs -ArgumentList \"-NoExit\""'

# --- Python/PIP ---
alias pipi='pip install'
alias pipu='python -m pip install --upgrade pip'
alias pyv='python --version'
alias pyi='python -m pip install'
alias venv='avenv'  # Alias to the avenv function
alias avenv='avenv'  # Main virtual environment activation
alias ven='avenv' #activate nearest venv (this and up dir)
alias dven='deactivate'
alias deven='deactivate'
alias dvenv='deactivate'  # Deactivate virtual 
alias devenv='dvenv'

alias vinfo='pyenv info'

alias wvenv='if [ -n "$VIRTUAL_ENV" ]; then echo "$VIRTUAL_ENV"; else echo "No venv active"; fi'  # Show current venv path

# create venv
alias pyvenv='python -m venv'





#uv pip install . in edit (dev) mode
alias uvpi="uv pip install"
alias uvpie="uv pip install -e ."
alias uvpi_e="uv pip install -e ."
alias upip="uv pip"

alias pyrun="python -m"

#python run
alias pym='python -m'
alias py='python'

# # * --- PATH Management ---
# # Add directories to PATH
# export PATH="$PATH"  # Initialize PATH if not set

#-------------------------
# ** project specific aliases 
#-------------------------
## flatmate

alias fm='flatmate' #flatmate installed in venv 



#-------------------------
# ** PATH Management **

syspath() {
    if [ -z "$1" ]; then
        echo "Current PATH:"
        echo "$PATH" | tr ':' '\n' | nl -ba
        return 0
    fi
    
    local dir="$1"
    # Convert Windows paths to Git Bash style if needed
    if [[ "$dir" == [A-Za-z]:\* ]]; then
        dir="/${dir:0:1}/${dir:3}"
    fi
    
    # Remove trailing slashes
    dir="${dir%/}"
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$dir:"* ]]; then
        export PATH="$PATH:$dir"
        echo "Added to PATH: $dir"
    else
        echo "Already in PATH: $dir"
    fi
}

# --- Editor ---
# np function is defined above (runs Notepad++ in background)


# --- OpenAI ---
alias oai="oai_key"
alias oai_key="export OPENAI_API_KEY='********************************************************************************************************************************************************************'"

####################################pyenv 
#             Functions            #
####################################

# Show Python environment information
pyinfo() {
    echo "Python Paths:"
    command -v python
    command -v python3
    echo -e "\nPip Paths:"
    command -v pip
    command -v pip3
    echo -e "\nPython Version:"
    python --version
    python -c "import sys; print(f'Executable: {sys.executable}')"
}

# Activate Python virtual environment (searches current and parent directories)
# Usage: avenv
# Looks for any directory containing 'venv' in its name
# Automatically finds and activates the nearest virtual environment
avenv() {
    local current_dir="$PWD"
    
    # Search up the directory tree
    while [ "$current_dir" != "/" ]; do
        # Find first venv directory in current directory
        local venv_dir
        venv_dir=$(find "$current_dir" -maxdepth 1 -type d -name "*venv*" | head -n 1)
        
        if [ -n "$venv_dir" ]; then
            echo "Activating virtual environment: $(basename "$venv_dir")"
            local activate_script
            activate_script="$venv_dir/Scripts/activate"
            if command -v cygpath >/dev/null 2>&1; then
                activate_script=$(cygpath -w "$activate_script" 2>/dev/null || echo "$activate_script")
            fi
            # shellcheck source=/dev/null
            . "$activate_script"
            return 0
        fi
        
        # Move up one directory
        current_dir=$(dirname "$current_dir")
    done
    
    echo "No virtual environment (*venv*) found in current or parent directories" >&2
    return 1
}

####################################
#         Oh My Posh Setup         #
####################################

####################################
#     Auto Virtual Environment     #
####################################
# Only auto-activate if not in a virtual environment already
if [ -z "$VIRTUAL_ENV" ] && [ -z "$NO_AUTO_VENV" ]; then
    # Try to find and activate a virtual environment
    avenv 2>/dev/null || true
fi
if command -v oh-my-posh &> /dev/null; then
    eval "$(oh-my-posh init bash --config "$HOME/.poshthemes/zash.omp.json")"
else
    # Fallback to a simple prompt
    PS1='\[\e[32m\]\w \$\[\e[0m\] '
fi

# End of bashrc.sh