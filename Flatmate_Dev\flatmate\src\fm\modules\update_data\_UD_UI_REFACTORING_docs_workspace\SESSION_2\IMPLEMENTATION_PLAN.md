# Implementation Plan: View Interface Pattern
**Date**: 2025-07-28  
**Session**: 2  
**Estimated Time**: 2.5 hours

## Phase 1: Interface Layer Creation (30 minutes)

### Task 1.1: Create Abstract Interface (15 minutes)

**File**: `src/fm/modules/update_data/interface/i_view_interface.py`

```python
"""
Abstract View Interface for Update Data Module.

Defines the contract between Presenter and View, breaking circular dependencies.
Based on analysis from REFACTOR_ANALYSIS.md
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
from PySide6.QtCore import QObject, Signal


class IUpdateDataView(QObject, ABC):
    """
    Abstract interface for Update Data View.
    
    Presenter depends only on this interface, never on concrete view.
    Concrete view implements this interface and handles Qt widget details.
    """
    
    # === SIGNALS (View → Presenter) ===
    cancel_clicked = Signal()
    source_select_requested = Signal()
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)
    
    # === STATE QUERIES (Presenter → View) ===
    @abstractmethod
    def get_save_option(self) -> str:
        """Get current save option selection."""
        pass
    
    @abstractmethod
    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        pass
    
    # === STATE UPDATES (Presenter → View) ===
    @abstractmethod
    def set_save_select_enabled(self, enabled: bool) -> None:
        """Enable/disable save selection controls."""
        pass
    
    @abstractmethod
    def set_source_option(self, option: str) -> None:
        """Set source option display text."""
        pass
    
    @abstractmethod
    def set_save_path(self, path: str) -> None:
        """Set save path display."""
        pass
    
    @abstractmethod
    def set_process_button_text(self, text: str) -> None:
        """Set process button text."""
        pass
    
    # === DIALOGS (Presenter → View) ===
    @abstractmethod
    def show_folder_dialog(self, title: str, initial_dir: str) -> str:
        """Show folder selection dialog."""
        pass
    
    @abstractmethod
    def show_files_dialog(self, title: str, initial_dir: str) -> List[str]:
        """Show file selection dialog."""
        pass
    
    @abstractmethod
    def show_error(self, message: str) -> None:
        """Show error message to user."""
        pass
    
    # === DISPLAY MANAGEMENT (Presenter → View) ===
    @abstractmethod
    def display_selected_source(self, source_data: Dict[str, Any]) -> None:
        """Display selected source information."""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Clean up view resources."""
        pass
    
    # === COMPONENT LIFECYCLE ===
    @abstractmethod
    def show_component(self) -> None:
        """Show the view component."""
        pass
    
    @abstractmethod
    def hide_component(self) -> None:
        """Hide the view component."""
        pass
```

### Task 1.2: Create Interface Implementation (15 minutes)

**File**: `src/fm/modules/update_data/interface/__init__.py`

```python
"""
Interface package for Update Data module.

Provides clean abstraction layer between Presenter and View.
"""

from .i_view_interface import IUpdateDataView

__all__ = ['IUpdateDataView']
```

## Phase 2: Presenter Updates (45 minutes)

### Task 2.1: Add State Dataclass (15 minutes)

**File**: `src/fm/modules/update_data/ud_presenter.py` (add at top)

```python
from dataclasses import dataclass
from .interface import IUpdateDataView

@dataclass
class UpdateDataUIState:
    """
    UI State for Update Data module.
    
    Presenter owns all UI flow decisions based on this state.
    """
    is_first_run: bool = True
    new_files_detected: bool = False
    source_folder_added: bool = False
    monitor_folder_enabled: bool = False
    files_selected: bool = False
    files_processed: bool = False
    save_folder_set: bool = False
    current_source_option: str = ""
    current_save_option: str = ""
    
    def can_process(self) -> bool:
        """Determine if processing can begin."""
        return self.files_selected and self.save_folder_set
```

### Task 2.2: Update Presenter Constructor (15 minutes)

```python
class UpdateDataPresenter:
    def __init__(self, main_window):
        self.main_window = main_window
        self.ui_state = UpdateDataUIState()
        self.view: IUpdateDataView = None  # Interface, not concrete view
        # ... rest of initialization
    
    def _create_view(self) -> IUpdateDataView:
        """Create view via interface."""
        from .ud_view import UpdateDataView  # Import here to avoid circular imports
        view = UpdateDataView(self.main_window)
        self._connect_view_signals(view)
        return view
    
    def _connect_view_signals(self, view: IUpdateDataView):
        """Connect to view interface signals."""
        view.cancel_clicked.connect(lambda: self.request_transition("home"))
        view.source_select_requested.connect(self._handle_source_select)
        view.save_select_requested.connect(self._handle_save_select)
        view.source_option_changed.connect(self._handle_source_option_change)
        view.save_option_changed.connect(self._handle_save_option_change)
        view.process_clicked.connect(self._handle_process)
        view.update_database_changed.connect(self._handle_update_database_change)
```

### Task 2.3: Replace View Calls with Interface (15 minutes)

Replace all `self.view.method()` calls with interface calls:

```python
# OLD: Direct view access
self.view.set_save_select_enabled(True)

# NEW: Interface access (same call, but now through interface)
self.view.set_save_select_enabled(True)

# Update state management
def _handle_source_option_change(self, option: str):
    self.ui_state.current_source_option = option
    self._reassess_ui_state()
    
def _reassess_ui_state(self):
    """Reassess UI state and update view accordingly."""
    can_process = self.ui_state.can_process()
    # Update view through interface
    self.view.set_process_button_text("Process Files" if can_process else "Select Files First")
```

## Phase 3: View Implementation (45 minutes)

### Task 3.1: Update View to Implement Interface (30 minutes)

**File**: `src/fm/modules/update_data/ud_view.py`

```python
from .interface import IUpdateDataView
from ._view_components.center_panel import CenterPanelManager
from ._view_components.left_panel import LeftPanelManager
from ._view_components.right_panel import RightPanelManager

class UpdateDataView(IUpdateDataView):
    """
    Concrete implementation of IUpdateDataView.
    
    Delegates interface calls to panel managers while maintaining
    clean abstraction for the presenter.
    """
    
    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        
        # Initialize panel managers
        self.center_panel = CenterPanelManager()
        self.left_panel = LeftPanelManager()
        self.right_panel = RightPanelManager()
        
        self._setup_ui()
        self._connect_internal_signals()
    
    def _connect_internal_signals(self):
        """Connect internal Qt signals to interface signals."""
        # Map Qt widget signals to interface signals
        self.left_panel.cancel_button.clicked.connect(self.cancel_clicked)
        self.left_panel.source_select_button.clicked.connect(self.source_select_requested)
        self.left_panel.save_select_button.clicked.connect(self.save_select_requested)
        self.left_panel.process_button.clicked.connect(self.process_clicked)
        
        # Option changes
        self.left_panel.source_option_changed.connect(self.source_option_changed)
        self.left_panel.save_option_changed.connect(self.save_option_changed)
        self.left_panel.update_database_changed.connect(self.update_database_changed)
    
    # === INTERFACE IMPLEMENTATION ===
    def get_save_option(self) -> str:
        """Delegate to left panel."""
        return self.left_panel.get_save_option()
    
    def get_update_database(self) -> bool:
        """Delegate to left panel."""
        return self.left_panel.get_update_database()
    
    def set_save_select_enabled(self, enabled: bool) -> None:
        """Delegate to left panel."""
        self.left_panel.set_save_select_enabled(enabled)
    
    def set_source_option(self, option: str) -> None:
        """Delegate to left panel."""
        self.left_panel.set_source_option(option)
    
    def set_save_path(self, path: str) -> None:
        """Delegate to appropriate panel."""
        self.left_panel.set_save_path(path)
        self.center_panel.set_save_path(path)
    
    # ... implement all other interface methods
```

### Task 3.2: Test Interface Implementation (15 minutes)

Create simple test to verify interface works:

```python
# Test script
def test_interface_implementation():
    from PySide6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    # Test interface import
    from fm.modules.update_data.interface import IUpdateDataView
    from fm.modules.update_data.ud_view import UpdateDataView
    
    # Test concrete implementation
    view = UpdateDataView(None)
    assert isinstance(view, IUpdateDataView)
    
    print("✓ Interface implementation successful")

if __name__ == "__main__":
    test_interface_implementation()
```

## Phase 4: Cleanup and Testing (30 minutes)

### Task 4.1: Deprecate Old Files (10 minutes)

Rename files with `z_deprecated_` prefix:
- `view_context_manager.py` → `z_deprecated_view_context_manager.py`
- `ui_modes.py` → `z_deprecated_ui_modes.py`
- `state_coordinator.py` → `z_deprecated_state_coordinator.py`

### Task 4.2: Update Imports (10 minutes)

Update any remaining imports to use new structure.

### Task 4.3: Integration Test (10 minutes)

```bash
# Test complete import chain
python -c "from fm.modules.update_data.ud_presenter import UpdateDataPresenter; print('✓ No circular imports')"

# Test application startup
python src/main.py
```

## Success Validation

- [ ] Interface can be imported independently
- [ ] Presenter can be imported without circular dependencies
- [ ] View implements all interface methods
- [ ] Application starts without import errors
- [ ] All existing functionality preserved

---

*This plan transforms the architecture from circular dependencies to clean interface-based separation.*
