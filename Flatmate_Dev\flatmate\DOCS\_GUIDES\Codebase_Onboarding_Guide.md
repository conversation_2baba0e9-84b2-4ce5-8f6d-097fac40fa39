# Flatmate Application Overview
**Last Updated**: 2025-01-29 @ SESSION_3
**Major Update**: Shared components usage patterns and deprecated functionality removal
## Core Architecture

USER NOTES = `>>` (Product Owner)
### Application Structure
- **Framework**: Modern Python desktop application built with **PySide6 (Qt)**.
- **Modularity**: Follows a modular architecture with a clear separation of concerns. Each functional area is a self-contained module.

### Entry Point: `main.py`
- Serves as the application's main entry point.
- Handles the initialisation of core components (e.g., logging, configuration, directories).
- Creates the main application window and starts the module coordinator.

### Module System
- **Coordinator**: `ModuleCoordinator` manages transitions and lifecycle for all application modules.
- **Modules**: Core features include `home`, `update_data` (for importing), and `categorize` (for viewing data).
- **Pattern**: Each module follows a **Presenter pattern** with its own dedicated UI components.

## Key Components

### Core Services
- **`logger.py`**: A centralised logging system provided through a singleton `log` object.
- **`event_bus.py`**: Manages event-driven communication between components.
- **`cache_service.py`**: Provides a caching mechanism for performance optimisation.

### Data Management
- **`DBIOService`**: A dedicated service for all database I/O operations, ensuring data persistence.
- **Statement Handlers**: Custom handlers for processing various financial statement formats.
- **Repositories**: Implements the repository pattern for data access (e.g., `SQLiteTransactionRepository`).

### GUI Architecture
- **`main_window.py`**: The main application window responsible for layout management.
- **Shared Components**: A library of shared components in `_shared_components` ensures a consistent UI.
  - **⚠️ CRITICAL**: Import shared components directly, not through GUI package to avoid circular imports
  - **Correct**: `from fm.gui._shared_components.base.base_panel_component import BasePanelComponent`
  - **Wrong**: `from fm.gui._shared_components import BasePanelComponent` (creates circular dependency)

#### **🚨 CRITICAL: Shared Components Usage Pattern**
- **Widget groups are defined in SHARED components** at `fm/gui/_shared_components/widgets/option_menus.py`
- **Module-specific implementations** are in `_view_components` folders
- **Required API should be defined in the generic base widget** - NOT in custom implementations
- **DO NOT create custom widgets** when shared components exist (e.g., `SelectOptionGroupVLayout`)
- **Example**: Use `SelectOptionGroupVLayout` from shared components instead of creating custom dropdown widgets

#### **Component Hierarchy**
- **Base widgets**: `fm/gui/_shared_components/widgets/` (e.g., `SelectOptionGroupVLayout`, `OptionMenuWithLabel`)
- **Module widgets**: `_view_components/` folders (connect and transform shared component signals)
- **Presenter**: Handles business logic via interface methods

- **Styling**: A consistent application appearance is maintained through a dedicated styling system.

## Design Patterns

### Presenter Pattern
- Each module has a dedicated presenter (e.g., `HomePresenter`, `UpdateDataPresenter`).
- Presenters contain the UI logic and coordinate with backend services.

### Repository Pattern
- Data access is abstracted through repository interfaces.
- This design allows for swapping database backends (currently SQLite) with minimal code changes.

### Singleton Pattern
- Ensures a single instance for critical services like logging (`log`) and database access (`DBIOService`).
- Provides consistent state and access across the application.

### Event-Driven Architecture
- Components communicate via an `event_bus`, reducing tight coupling between modules.

## Code Style and Principles

### Code Quality
- **Readability**: Favours explicit and readable code over complex one-liners.
- **Organisation**: Files are well-organised with clear sections and comments.

### Error Handling
- **Philosophy**: Minimal `try/except` blocks are used to ensure errors are immediately visible ("fail fast").

### Logging
- **Implementation**: A custom logger provides color-coded console output.
- **Configuration**: Logging is configured centrally.
from fm.core.logger import log (check)
syntax 
log.critical(<message>)
log.debug(<message>)
log.info(<message>)
log.warning(<message>)
log.error(<message>)

### Configuration System
- **Management**: A centralised system manages all application settings.
- **Type Safety**: Keys are defined in `ConfigKeys` for type safety and easy discoverability.
>> There is an experimental system in categorise - it uses a different approach, "ensure_defaults" KVs are written to the YAAML - to avoid speculutive kv's getting into configuration - but inherits the local_config from core config and therefore has access to core config key values
it does not as yet have a config keys enum.... this is still under development. 
#>> # #TODO: this part of the unboarding needs updating.

---

This codebase is a well-structured financial management application with a strong focus on modularity, maintainability, and a consistent user experience.

# 2025-07-27 @ 03:08:11
>> We are currently working on rationaling update data specifically around the view and state management but also dir structure, logical flow and file organisation ...
Refactoring has proved very difficult - we attempting to apply an~~ event based state management system...~~ (scrapped) we are staying with MVP but implementing an i_view interface layer to improve decoupling and testability.
The current objective is to migrate the existing code base to the new system and and then convert to the new logical flow, based around:
~\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\_UD_VIEW_docs_workspace\_USER_JORNEY_FLOW_v2.md

In update_data\_UD_VIEW_docs_workspace
you wil find the current working documents. 


Watch out for nheritance patterns, these are intended to give common methods, and interfaces but do not attempt to make a generic base widget conflig class that micromanages every widget
-  certainly not using ABC base classes
These will not work with the QT pyside classes- they cant be inherited at the same time and cause a meta class conflict.
base classes inherit qt - generic widgets inherit from base classes - specific widgets in modules inherit from generic widgets in 

## >> gui/_shared_components
# >> at some point we will sort dir stucture 
/**/gui/_shared
               /components (widets) 
                  perhaps: /composite_widgets
                           /widegets
               /base 
                  -panel => BasePanel
                  -pane => BasePane
                  -widget => BaseWidget

---

## ⚠️ Critical Development Guidelines for AI Assistants

### Import Management (CRITICAL)
**Problem**: Circular imports can break the entire module system.

**Root Cause**: AI assistants often create complex import chains without understanding dependency direction.

**Solution**: Always import shared components directly:
```python
# ✅ CORRECT - Direct import
from fm.gui._shared_components.base.base_panel_component import BasePanelComponent

# ❌ WRONG - Creates circular dependency
from fm.gui._shared_components import BasePanelComponent
```

### Architectural Anti-Patterns to Avoid
1. **Lazy Loading as Default Solution**
   - Often indicates architectural problems
   - Use only when genuinely needed for performance
   - Direct imports are usually better
   - Consider dependancy injection and or an interface 

3. **Complex State Management**
   - Avoid over-engineering state systems
   - Simple, explicit state handling is preferred
   - Event-driven architecture should be incremental, not revolutionary

### Dependency Direction Rules
```
✅ CORRECT Flow:
Modules → Shared Components (independent)
GUI → MainWindow → ModuleCoordinator → Modules

❌ WRONG Flow:
Modules → SharedComponents → GUI → MainWindow → ModuleCoordinator → Modules (circular)
```

### Recent Resolution Example (2025-07-28)
**Issue**: Update data module had circular imports
**AI Attempted**: Complex abstractions and lazy loading
**Actual Solution**: Fixed single import path in center_panel.py
**Lesson**: Trust developer instincts - "if it reeks of architectural flaws," investigate root cause




## 🚨 Deprecated Functionality (Session 3 - Jan 29, 2025)

### **Auto-Import System - SCRAPPED**
- **Status**: Completely removed from codebase
- **Reason**: Architectural complexity, replaced with simpler monitor folder option
- **Replacement**: "Monitor this folder for new files" checkbox in guide pane
- **Key Learning**: Guide pane is the proper place for contextual UI options

### **View Manager Pattern - DEPRECATED**
- **Issue**: Code references missing `self.view_manager` causing AttributeError
- **Solution**: Use interface methods directly instead of manager classes
- **Pattern**: Presenter → Interface Methods → View Components

## 📋 Current Patterns (Updated Jan 29, 2025)

### **Guide Pane Usage**
- **Purpose**: Contextual UI options based on current state
- **Example**: Monitor folder checkbox appears when source and archive configured
- **Signal Flow**: `guide_pane.message_changed` → `presenter._handle_guide_pane_message`

### **State-Driven UI Updates**
- **Pattern**: Presenter updates guide pane based on state changes
- **Method**: `_update_guide_pane()` in presenter handles contextual messaging
- **Options**: Use `add_checkbox_option()` for user preferences

# Tuesday, July 29, 2025 @ 12:44:03 PM
>>
### Update Data UI Refactoring Progress
- View interface reinstated and state shifted to ud_presenter in session_2
- Current session folder is SESSION_3 in the docs workspace: `update_data\_UD_UI_REFACTORING_docs_workspace`
- We have shifted focus to implementing [User_FLOW_v3](<../../src/fm/modules/update_data/_UD_UI_REFACTORING_docs_workspace/_USER_FLOW_v3.md>)
- **SESSION_3 COMPLETED**: Fixed shared components usage, removed auto-import, implemented monitor folder in guide pane
- The current implementation plan is in [implementation_plan.md](<../../src/fm/modules/update_data/_UD_UI_REFACTORING_docs_workspace/SESSION_3/implementation_plan.md>)


### Workflow Insights
- **View Interface Pattern**: The presenter interacts with the view only through interface methods, maintaining clean separation from Qt widgets
- **Options Management**: Presenter owns state but delegates widget configuration to the view through interface methods
- **Component Responsibility**: Each component file maintains clear, focused responsibility rather than creating "god classes"
- **Incremental Testing**: Each UI component should be tested immediately after implementation to validate against the spec

# >> 2025-07-29 @ 14:16:29
READ THE DOCS! - Review all documentation in the SESSION_3 folder and study the affected files before making changes.
[flatmate\src\fm\modules\update_data\_UD_UI_REFACTORING_docs_workspace\SESSION_3](../../src/fm/modules/update_data/_UD_UI_REFACTORING_docs_workspace/SESSION_3)