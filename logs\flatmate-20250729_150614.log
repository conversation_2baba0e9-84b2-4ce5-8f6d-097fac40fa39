2025-07-29 15:06:14 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-29 15:06:19 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-29 15:06:19 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-29 15:06:19 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-29 15:06:19 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-29 15:06:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded 0 component defaults for categorize from C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\config\defaults.yaml
2025-07-29 15:06:19 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 0 user preferences
2025-07-29 15:06:19 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-29 15:06:19 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-29 15:06:20 - [main] [INFO] - Application starting...
2025-07-29 15:06:33 - [main] [INFO] - 
=== Initializing Database & Cache ===
2025-07-29 15:06:33 - [fm.core.data_services.db_io_service] [INFO] - Initializing DBIOService singleton...
2025-07-29 15:06:33 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-29 15:06:33 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - CachedSQLiteRepository initialized
2025-07-29 15:06:33 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Warming transaction cache...
2025-07-29 15:06:34 - [fm.core.database.sql_repository.cached_sqlite_repository] [INFO] - Cache warmed successfully: 2099 transactions, 3 unique accounts in 1.61s
2025-07-29 15:06:34 - [fm.core.data_services.db_io_service] [INFO] - DBIOService initialized with cache: 2099 transactions
2025-07-29 15:06:34 - [main] [INFO] - 
=== Initializing Auto-Import Manager ===
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] - Auto-import paths configured:
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] -   Import: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] -   Archive: C:\Users\<USER>\OneDrive\Documents\ACCOUNTS\Bank_Statement_dwnlds_2025\fm_auto_imported
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] -   Failed: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] - AutoImportManager initialized - monitoring: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] - Auto-import worker thread started
2025-07-29 15:06:34 - [fm.core.services.auto_import_manager] [INFO] - Auto-import monitoring started for: C:\Users\<USER>\Downloads\_flatmete_auto_import
2025-07-29 15:06:34 - [main] [INFO] - Auto-import monitoring started
2025-07-29 15:06:34 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-29 15:06:34 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-29 15:06:34 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-29 15:06:34 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-29 15:06:34 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-29 15:06:34 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-29 15:06:34 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-29 15:06:34 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-29 15:06:35 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-29 15:06:35 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-29 15:06:35 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-29 15:06:35 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-29 15:06:35 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-29 15:06:35 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-29 15:06:35 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected and event bridges set up
2025-07-29 15:06:35 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-29 15:06:35 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-29 15:06:35 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-29 15:06:36 - [fm.core.database.sql_repository.cached_sqlite_repository] [DEBUG] - Cache hit: returning 3 unique accounts
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-29 15:06:36 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-29 15:06:36 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.105s
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-29 15:06:36 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-07-29 15:06:36 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-29 15:06:36 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-29 15:06:36 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions as DataFrame
2025-07-29 15:06:36 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.115s
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2099, 32), empty: False
2025-07-29 15:06:36 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 15:06:37 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  ...  is_deleted
0  38-9004-0646977-04  ...           0
1  38-9004-0646977-04  ...           0
2  38-9004-0646977-04  ...           0
3  38-9004-0646977-04  ...           0
4  38-9004-0646977-00  ...           0

[5 rows x 32 columns]
2025-07-29 15:06:37 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2099, 32)
2025-07-29 15:06:37 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-07-29 15:06:37 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-29 15:06:37 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.122s
2025-07-29 15:06:37 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-07-29 15:06:37 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-07-29 15:06:37 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.008s
2025-07-29 15:06:37 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-07-29 15:06:37 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-29 15:06:37 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-29 15:06:37 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 15:06:37 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-29 15:06:37 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Using ordered display columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes']
2025-07-29 15:06:37 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Reordered DataFrame columns: ['date', 'details', 'amount', 'account', 'balance', 'category', 'tags', 'notes', 'credit_amount', 'db_uid', 'debit_amount', 'empty', 'hash', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tp_code', 'tp_part', 'tp_ref', 'unique_id', 'id', 'import_date', 'modified_date', 'is_deleted']
2025-07-29 15:06:44 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Date', 'Details', 'Amount', 'Account', 'Balance', 'Category', 'Tags', 'Notes']
2025-07-29 15:06:44 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 7.321s
2025-07-29 15:06:44 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 7.7s (271.5 txns/s)
2025-07-29 15:06:44 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 7.842s
2025-07-29 15:06:44 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-07-29 15:06:44 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-29 15:06:44 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-07-29 15:06:44 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
