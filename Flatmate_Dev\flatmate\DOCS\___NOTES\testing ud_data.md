# testing ud_data

1. ADRESS THESE ERRORS

Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 45, in <lambda>
    lambda: self.request_transition("update_data")
            ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 121, in transition_to    self.current_module.show(**params)
    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 118, in show    self._refresh_content(**params)
    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\update_data\ud_presenter.py", line 306, in _refresh_content
    if is_database_mode and auto_import_status.get('enabled', False):
                            ^^^^^^^^^^^^^^^^^^
NameError: name 'auto_import_status' is not defined
TypeError: source_select_requested() only accepts 0 argument(s), 1 given!
TypeError: source_select_requested() only accepts 0 argument(s), 1 given!


2, archive location group:

no terminal output when opt menu changed 
no terminal output when select btn clicked

>> do we need to know when the selected option is changed ? yes. because when Same as source ifles is selected - the opt menus select button should be deactivated
>> where should this be handled - in the presenter or the view? - or in left panel.py !?

The process fles button should be deselected until all required conditionsw are met
where and how should this be handled
Given updates data structure and best patterns in an mvp with an interface !?