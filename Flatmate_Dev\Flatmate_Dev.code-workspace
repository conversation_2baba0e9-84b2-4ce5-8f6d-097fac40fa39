{
    "folders": [
        {
            "path": "."
        },
        {
            "path": "../../../.flatmate"
        },
        {
            "path": "../../../_scripts"
        },
        {
            "path": "../../DEV_TOOLS/bash_workbench"
        }
    ],
    "settings": {
        "python.defaultInterpreterPath": "${workspaceFolder}/flatmate/.venv_fm313/Scripts/python.exe",
        "terminal.integrated.cwd": "${workspaceFolder}/flatmate",
        "terminal.integrated.defaultProfile.windows": "Git Bash",
        "files.exclude": {
            "**/.git": true,
            "**/.gitignore": false,
            "**/.gitmodules": false,
            "**/.hg": true,
            "**/.svn": true,
            "**/.DS_Store": true,
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/*.pyo": true
        },
        // "folder-color.pathColors": [],
        "catppuccin-icons.associations.folders": {
            "_docs": "folder-lib",
            "_DOCS": "folder-lib",
            "_UD_DOCS": "folder-lib",
            "_ud_docs": "folder-lib",
            "_CAT_DOCS": "folder-lib",
            "_cat_docs": "folder-lib",
            "DOCS_WORKSPACE": "folder-tools",
            "_DOCS_WORKSPACE": "folder-tools",
            "docs_workspace": "folder-tools",
            "_WORKSPACE": "folder-tools",
            "_DEVELOPMENT_workspace": "folder-tools",
            "_MODULE_DEV_workspace": "folder-tools"
            // doesnt fukn work
        },
        "todo-tree.general.fileWatcherGlob": "**/*src",
        "git.autoStash": true,
        "explorer.sortOrder": "filesFirst"
    }
}