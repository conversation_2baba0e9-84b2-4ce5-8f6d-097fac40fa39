# Interface Design Specification
**Date**: 2025-07-28  
**Based on**: REFACTOR_ANALYSIS.md findings

## Interface Architecture

### Design Principle
The `IUpdateDataView` interface serves as a **contract** between the Presenter and View, ensuring:
- **Zero Qt coupling** in the presenter
- **Testable architecture** via mock implementations  
- **Clean separation** of business logic and UI concerns
- **Broken circular dependencies** through abstraction

### Communication Patterns

```
┌─────────────────┐    Interface     ┌─────────────────┐
│   Presenter     │◄─────────────────┤   IUpdateDataView│
│                 │                  │   (Abstract)    │
│ - Business Logic│                  │                 │
│ - State Owner   │                  │ - Signals       │
│ - UI Controller │                  │ - Methods       │
└─────────────────┘                  └─────────────────┘
         ▲                                     ▲
         │                                     │
         │ Uses Interface                      │ Implements
         │                                     │
         │                           ┌─────────────────┐
         └───────────────────────────┤ UpdateDataView  │
                                     │ (Concrete)      │
                                     │                 │
                                     │ - Qt Widgets    │
                                     │ - Panel Managers│
                                     │ - UI Logic      │
                                     └─────────────────┘
```

## Signal Design

### View → Presenter (User Actions)
```python
# High-level domain events, not widget-specific signals
cancel_clicked = Signal()                    # User wants to exit
source_select_requested = Signal()          # User wants to select source
save_select_requested = Signal()            # User wants to select save location
source_option_changed = Signal(str)         # Source option dropdown changed
save_option_changed = Signal(str)           # Save option dropdown changed  
process_clicked = Signal()                  # User wants to start processing
update_database_changed = Signal(bool)      # Database update checkbox toggled
```

**Key Design Decision**: Signals represent **user intentions**, not widget events.
- ✅ `source_select_requested` (intention)
- ❌ `source_button_clicked` (widget event)

## Method Design

### State Queries (Presenter → View)
```python
def get_save_option(self) -> str:
    """Get current save option selection."""
    
def get_update_database(self) -> bool:
    """Get update database checkbox state."""
```

**Design Principle**: Presenter queries view state when needed, view doesn't push state.

### State Updates (Presenter → View)
```python
def set_save_select_enabled(self, enabled: bool) -> None:
    """Enable/disable save selection controls."""
    
def set_source_option(self, option: str) -> None:
    """Set source option display text."""
    
def set_save_path(self, path: str) -> None:
    """Set save path display."""
    
def set_process_button_text(self, text: str) -> None:
    """Set process button text."""
```

**Design Principle**: Presenter tells view **what to display**, not **how to display it**.

### Dialog Management (Presenter → View)
```python
def show_folder_dialog(self, title: str, initial_dir: str) -> str:
    """Show folder selection dialog."""
    
def show_files_dialog(self, title: str, initial_dir: str) -> List[str]:
    """Show file selection dialog."""
    
def show_error(self, message: str) -> None:
    """Show error message to user."""
```

**Design Principle**: View handles all UI dialogs, presenter provides parameters.

### Display Management (Presenter → View)
```python
def display_selected_source(self, source_data: Dict[str, Any]) -> None:
    """Display selected source information."""
    
def cleanup(self) -> None:
    """Clean up view resources."""
```

**Design Principle**: Presenter provides data, view decides presentation.

## Implementation Mapping

### Interface → Panel Manager Delegation
```python
class UpdateDataView(IUpdateDataView):
    def set_source_option(self, option: str) -> None:
        """Interface method delegates to panel manager."""
        self.left_panel.set_source_option(option)
    
    def set_save_path(self, path: str) -> None:
        """Interface method can delegate to multiple panels."""
        self.left_panel.set_save_path(path)
        self.center_panel.update_save_display(path)
```

### Signal Routing
```python
def _connect_internal_signals(self):
    """Map Qt widget signals to interface signals."""
    # Qt widget signal → Interface signal
    self.left_panel.source_button.clicked.connect(
        self.source_select_requested.emit
    )
    
    # Complex signal mapping
    self.left_panel.source_dropdown.currentTextChanged.connect(
        lambda text: self.source_option_changed.emit(text)
    )
```

## Testing Strategy

### Mock Implementation
```python
class MockUpdateDataView(IUpdateDataView):
    """Mock implementation for presenter testing."""
    
    def __init__(self):
        super().__init__()
        self.method_calls = []
        self.state = {}
    
    def set_source_option(self, option: str) -> None:
        self.method_calls.append(('set_source_option', option))
        self.state['source_option'] = option
    
    def get_save_option(self) -> str:
        return self.state.get('save_option', '')
```

### Presenter Testing
```python
def test_presenter_source_selection():
    mock_view = MockUpdateDataView()
    presenter = UpdateDataPresenter(mock_view)
    
    # Test presenter logic
    presenter._handle_source_option_change("folder")
    
    # Verify interface calls
    assert ('set_source_option', 'folder') in mock_view.method_calls
```

## Benefits Achieved

### 1. Broken Circular Dependencies
- Presenter depends on interface (abstract)
- View implements interface (concrete)
- No upward dependencies from view to presenter

### 2. Testable Architecture
- Mock interface for presenter testing
- Test view implementation separately
- Clear contract verification

### 3. Clean Separation of Concerns
- Presenter: Business logic + UI flow control
- Interface: Contract definition
- View: Qt implementation + UI presentation

### 4. Maintainable Code
- Changes to Qt widgets don't affect presenter
- Interface changes are explicit and versioned
- Clear boundaries between components

---

*This interface design provides the foundation for clean, testable, and maintainable presenter-view separation.*
