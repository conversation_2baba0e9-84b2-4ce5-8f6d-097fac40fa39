"""
Update Data view implementation.
"""

import os
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Optional, Dict, Any

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFileDialog, QMessageBox

from ...core.services.event_bus import global_event_bus
from ..base.base_module_view import BaseModuleView
from ._view_components.center_panel import CenterPanelManager
from ._view_components.left_panel import LeftPanelManager
from .services.local_event_bus import (
    EventDataFactory,
    ViewEvents,
    update_data_local_bus,
)
from .interface import IUpdateDataView


# Interface now imported from separate package to avoid circular dependencies


class UpdateDataView(BaseModuleView):
    """
    Update Data view.

    Implements IUpdateDataView interface methods for presenter interaction.
    Note: Does not inherit from Protocol to avoid metaclass conflicts with Qt.
    """
    # ------------------
    # Signals (Interface Implementation)
    # ------------------
    cancel_clicked = Signal()
    source_select_requested = Signal()
    save_select_requested = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    process_clicked = Signal()
    update_database_changed = Signal(bool)


    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        """Initialize the view.

        Args:
            parent: Parent widget
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Initialize basic properties first
        self.event_bus = global_event_bus
        # MIGRATION: Add local event bus for internal coordination
        self.local_bus = update_data_local_bus
        # Then call parent constructor which will call setup_ui
        super().__init__(parent, gui_config, gui_keys)

    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # MIGRATION: Create proper panel managers instead of direct widgets
        self.left_panel_manager = LeftPanelManager()
        self.center_display = CenterPanelManager()

        # Create guide pane for contextual feedback
        self._create_guide_pane()

        # MIGRATION: Subscribe to events instead of just connecting signals
        self._subscribe_to_events()
        self._connect_signals()

    def setup_left_panel(self, layout):
        """Set up the left panel content in the base class container."""
        # Add the left panel manager to the container layout
        layout.addWidget(self.left_panel_manager)

    def _subscribe_to_events(self):
        """
        Subscribe to local events for UI updates.

        MIGRATION: View now reacts to events instead of direct method calls.
        """
        self.local_bus.subscribe(ViewEvents.UI_STATE_CHANGED.value, self.update_ui_state)
        self.local_bus.subscribe(ViewEvents.STATUS_MESSAGE_CHANGED.value, self.update_status_message)
        self.local_bus.subscribe(ViewEvents.FILE_DISPLAY_UPDATED.value, self.update_files_display)

    def _connect_signals(self):
        """
        Connect internal signals to be forwarded.

        MIGRATION: Now also emits local events alongside Qt signals.
        """
        # Forward all signals from left panel manager (not directly from widgets)
        self.left_panel_manager.buttons_widget.source_select_requested.connect(
            lambda selection_type: self._emit_user_action(ViewEvents.SOURCE_SELECT_REQUESTED,
                                                         EventDataFactory.source_select_request(selection_type))
        )
        self.left_panel_manager.buttons_widget.source_select_requested.connect(self.source_select_requested.emit)

        self.left_panel_manager.buttons_widget.save_select_requested.connect(
            lambda: self._emit_user_action(ViewEvents.DESTINATION_SELECT_REQUESTED, {})
        )
        self.left_panel_manager.buttons_widget.save_select_requested.connect(self.save_select_requested.emit)

        self.left_panel_manager.buttons_widget.process_clicked.connect(
            lambda: self._emit_user_action(ViewEvents.PROCESS_REQUESTED, self._get_process_context())
        )
        self.left_panel_manager.buttons_widget.process_clicked.connect(self.process_clicked.emit)

        self.left_panel_manager.buttons_widget.cancel_clicked.connect(
            lambda: self._emit_user_action(ViewEvents.CANCEL_REQUESTED, {})
        )
        self.left_panel_manager.buttons_widget.cancel_clicked.connect(self.cancel_clicked.emit)

        self.left_panel_manager.buttons_widget.archive_option_changed.connect(self.save_option_changed.emit)
        self.left_panel_manager.buttons_widget.update_database_changed.connect(self.update_database_changed.emit)

    def _emit_user_action(self, event_type: ViewEvents, data: dict):
        """Helper method to emit user action events."""
        self.local_bus.emit(event_type.value, data)

    def _get_process_context(self) -> dict:
        """Get context data for processing request."""
        return {
            'update_database': self.get_update_database(),
            'save_option': self.left_panel_manager.buttons_widget.get_save_option() if hasattr(self.left_panel_manager.buttons_widget, 'get_save_option') else 'same_as_source'
        }

    # ------------------
    # Event Reaction Methods (MIGRATION)
    # ------------------

    def update_ui_state(self, ui_state_data):
        """
        React to UI state changes via events.

        MIGRATION: Replaces direct method calls from state coordinator.
        """
        # Update process button
        if 'can_process' in ui_state_data:
            self.set_process_enabled(ui_state_data['can_process'])

        # Update archive section
        if 'archive_enabled' in ui_state_data:
            self.set_archive_enabled(ui_state_data['archive_enabled'])

        # Update process button text
        if 'process_button_text' in ui_state_data:
            self.set_process_text(ui_state_data['process_button_text'])

        # Update processing state
        if 'processing' in ui_state_data:
            self.set_all_controls_enabled(not ui_state_data['processing'])

    def update_status_message(self, message_data):
        """
        React to status message changes via events.

        MIGRATION: Replaces direct guide pane calls.
        """
        if hasattr(self, 'guide_pane') and 'message' in message_data:
            self.guide_pane.display(message_data['message'])

    def update_files_display(self, files_data):
        """
        React to files display updates via events.

        MIGRATION: New event-driven file display updates.
        """
        if 'files' in files_data and 'source_path' in files_data:
            self.center_display.set_files(files_data['files'], files_data['source_path'])

    # setup_left_panel method already defined above - removing duplicate

    def setup_center_panel(self, layout):
        """Set up the center panel with display areas."""
        layout.addWidget(self.center_display)

    def disconnect_signals(self):
        """Clean up signal connections."""
        if hasattr(self, "left_panel_manager"):
            self.left_panel_manager.buttons_widget.source_select_requested.disconnect()
            self.left_panel_manager.buttons_widget.save_select_requested.disconnect()
            self.left_panel_manager.buttons_widget.process_clicked.disconnect()
            self.left_panel_manager.buttons_widget.cancel_clicked.disconnect()
            self.left_panel_manager.buttons_widget.source_option_changed.disconnect()
            self.left_panel_manager.buttons_widget.save_option_changed.disconnect()
            self.left_panel_manager.buttons_widget.update_database_changed.disconnect()

    # ------------------
    # UI State Methods
    # ------------------
    def set_exit_mode(self):
        """Set left panel to exit mode."""
        self.left_panel_manager.set_exit_mode()

    def set_process_mode(self):
        """Set left panel to process mode."""
        self.left_panel_manager.set_process_mode()

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save select button."""
        self.left_panel_manager.buttons_widget.save_group.set_button_enabled(enabled)

    def get_update_database(self) -> bool:
        """Get the current state of the update database checkbox."""
        return self.left_panel_manager.buttons_widget.db_update_checkbox.is_checked()

    def set_source_option(self, option: str):
        """Set the source option in the left panel."""
        self.left_panel_manager.buttons_widget.source_group.set_selected_option(option)

    def set_update_database(self, checked: bool):
        """Set the state of the update database checkbox."""
        self.left_panel_manager.buttons_widget.db_update_checkbox.set_checked(checked)

    # ------------------
    # SimpleStateCoordinator Interface Methods
    # MIGRATION: These methods are now called via events (update_ui_state)
    # but kept for backward compatibility during migration
    # ------------------
    def set_process_enabled(self, enabled: bool):
        """Enable/disable the process button."""
        print(f"[STATE_COORDINATOR] Setting process button enabled: {enabled}")  # Debug logging
        if hasattr(self.left_panel_manager.buttons_widget, 'process_btn'):
            self.left_panel_manager.buttons_widget.process_btn.setEnabled(enabled)

    def set_archive_enabled(self, enabled: bool):
        """Enable/disable the archive section."""
        print(f"[STATE_COORDINATOR] Setting archive section enabled: {enabled}")  # Debug logging
        self.set_save_select_enabled(enabled)

    def set_process_text(self, text: str):
        """Set the process button text."""
        print(f"[STATE_COORDINATOR] Setting process button text: {text}")  # Debug logging
        if hasattr(self.left_panel_manager.buttons_widget, 'process_btn'):
            self.left_panel_manager.buttons_widget.process_btn.setText(text)

    def set_all_controls_enabled(self, enabled: bool):
        """Enable/disable all controls during processing."""
        if hasattr(self.left_panel_manager.buttons_widget, 'process_btn'):
            self.left_panel_manager.buttons_widget.process_btn.setEnabled(enabled)
        if hasattr(self.left_panel_manager.buttons_widget, 'source_select_btn'):
            self.left_panel_manager.buttons_widget.source_select_btn.setEnabled(enabled)
        if hasattr(self.left_panel_manager.buttons_widget, 'save_select_btn'):
            self.left_panel_manager.buttons_widget.save_select_btn.setEnabled(enabled)

    # ------------------
    # Display Methods - interface for presenter
    # ------------------

    def set_save_path(self, path: str):
        """Set the save location path in the center panel."""
        self.center_display.set_save_path(path)

    def display_selected_source(self, source_info: dict):
        """Display the selected source files in the center panel."""
        if not source_info:
            return

        if source_info["type"] == "folder":
            self.center_display.set_source_path(source_info["path"])
            # Pass the full file paths to the file browser
            self.center_display.set_files(
                source_info["file_paths"], source_info["path"]
            )
        else:  # files
            files = source_info["file_paths"]
            source_dir = os.path.dirname(files[0])
            self.center_display.set_source_path(source_dir)
            # Pass the full file paths to the file browser
            self.center_display.set_files(files, source_dir)

    def display_master_csv(self, df: pd.DataFrame):
        """Display a DataFrame in the center panel table."""
        self.center_display.display_master_csv(df)

    def display_welcome(self):
        """Display welcome message in center panel."""
        self.center_display.display_welcome()

    # ------------------
    # Info Methods
    # ------------------
    # Status bar-related methods removed as they're redundant
    # The presenter should use InfoBarService directly or publish events
    # through the event bus. The UpdateDataStatusBar now subscribes to
    # these events directly.

    # ------------------
    # Dialog Methods
    # ------------------
    def show_error(self, message: str, title: str = "Error"):
        """Show error message box."""
        QMessageBox.critical(self, title, message)

    def show_success(self, message: str, title: str = "Success"):
        """Show success message box."""
        QMessageBox.information(self, title, message)

    def get_save_option(self) -> str:
        """Get the current save option from the left panel buttons."""
        return self.left_panel_manager.buttons_widget.get_save_option()

    def show_folder_dialog(self, title: str, initial_dir: Optional[str] = None) -> str:
        """Show a folder selection dialog and return the selected folder path."""
        # Use proper folder dialog on all platforms
        return QFileDialog.getExistingDirectory(
            self,
            title,
            initial_dir or str(Path.home()),
            QFileDialog.Option.ShowDirsOnly
        )

    def show_files_dialog(
        self,
        title: str,
        initial_dir: Optional[str] = None,
        filter_str: str = "Data Files (*.csv *.CSV)",
    ) -> List[str]:
        """Show a file selection dialog and return the selected file paths."""
        files, _ = QFileDialog.getOpenFileNames(
            self, title, initial_dir or "", filter_str
        )
        return files

    def _create_guide_pane(self):
        """Create guide pane with lazy import to avoid circular dependencies."""
        from ._view_components.center_panel_components.guide_pane import GuidePaneWidget
        self.guide_pane = GuidePaneWidget()

    # ------------------
    # Interface Implementation Methods
    # ------------------

    def get_save_option(self) -> str:
        """Get current save option selection."""
        # TODO: Implement based on left panel state
        return "csv"  # Default for now

    def get_update_database(self) -> bool:
        """Get update database checkbox state."""
        # TODO: Implement based on left panel checkbox
        return True  # Default for now

    def set_save_select_enabled(self, enabled: bool) -> None:
        """Enable/disable save selection controls."""
        # TODO: Implement left panel control enabling
        pass

    def set_source_option(self, option: str) -> None:
        """Set source option display text."""
        # TODO: Implement left panel source option display
        pass

    def set_save_path(self, path: str) -> None:
        """Set save path display."""
        # TODO: Implement save path display in center panel
        pass

    def set_process_button_text(self, text: str) -> None:
        """Set process button text."""
        # TODO: Implement process button text update
        pass

    def show_folder_dialog(self, title: str, initial_dir: str) -> str:
        """Show folder selection dialog."""
        folder = QFileDialog.getExistingDirectory(self, title, initial_dir)
        return folder if folder else ""

    def display_selected_source(self, source_data: Dict[str, Any]) -> None:
        """Display selected source information."""
        # TODO: Implement source display in center panel
        pass

    def cleanup(self) -> None:
        """Clean up view resources."""
        # Disconnect signals and clean up
        try:
            self._disconnect_signals()
        except:
            pass

    def show_component(self) -> None:
        """Show the view component."""
        self.show()

    def hide_component(self) -> None:
        """Hide the view component."""
        self.hide()


