# Custom Bashrc Workflow

This directory contains a custom `bashrc.sh` file and supporting workflow for managing your Bash configuration on Windows (or WSL/Git Bash).

## Files
- **bashrc.sh**: Your main, version-controlled Bash configuration file. Edit this file to manage aliases, functions, and environment settings.
- **README.md**: This documentation file.

## Workflow

### 1. Edit `bashrc.sh`
Make your changes or add new aliases/functions directly in `bashrc.sh`.

### 2. Update your live Bash config
To copy your custom config to your home directory's `.bashrc`, run:
```bash
flashbash
```
This uses the alias:
```bash
alias flashbash='cp ~/OneDrive/Documents/_DEV/bash/bashrc.sh ~/.bashrc'
```

### 2b. Update your Oh My Posh theme
To copy your custom prompt theme to your Oh My Posh themes directory, run:
```bash
flashzash
```
This uses the alias:
```bash
alias flashzash='cp ~/OneDrive/Documents/_DEV/bash/zash.omp.json ~/.poshthemes/zash.omp.json'
```
After running `flashzash`, restart your terminal or reload your prompt to see the new theme in effect.

### 3. Reload Bash config
To apply changes immediately in your current terminal session, run:
```bash
src
```
This uses the alias:
```bash
alias src='source ~/.bashrc'
```

### 4. Confirm update worked
Run:
```bash
vfbash
```
You should see:
```
flashed bashrc is alive! 🦾
```

### 5. List all aliases
Run:
```bash
alias
```
or
```bash
aliases
```
(for a sorted list)

## Tips
- You can edit `bashrc.sh` in Notepad++ using the `np` alias.
- To avoid losing your previous `.bashrc`, you can manually back it up:
  ```bash
  cp ~/.bashrc ~/.bashrc.bak
  ```
- To add a new test alias, just add it to `bashrc.sh`, repeat steps 2 and 3, and test it!

## Customization
- Change the message or add a version number to the `vfbash` alias for more granular confirmation.
- Extend the workflow with more automation or backup steps as needed.

---

**Happy hacking!**
