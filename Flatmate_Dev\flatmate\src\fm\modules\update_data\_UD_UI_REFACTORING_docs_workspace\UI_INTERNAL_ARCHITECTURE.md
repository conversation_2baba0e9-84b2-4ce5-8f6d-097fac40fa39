# Update Data: UI Internal Architecture

**Date**: 2025-07-28
**Author**: Cascade
**Status**: Proposed Design

## 1. The UI as an API 

This document illustrates how the `IUpdateDataView` interface acts as a strict API between the `Presenter` (business logic) and the `View` (UI implementation). The Presenter is completely decoupled from any Qt-specific code; it only knows how to talk to the methods and signals defined in the interface contract.

## 2. Component Relationship Diagram

The diagram below shows the key relationships. The `IUpdateDataView` interface forms a hard boundary that the Presenter logic cannot cross.

```mermaid
flowchart TD
    User([User]) -- "1. Clicks" --> <PERSON><PERSON>[QPushButton]
    Button -- "2. Raw Qt Signal" --> Panel[Panel Component]
    Panel -- "3. Internal Signal" --> View[UpdateDataView]
    View -- "4. Emits API Signal" --> Interface(IUpdateDataView API)
    Interface -- "5. Presenter Listens" --> Presenter[UpdateDataPresenter]
    Presenter -- "6. Issues API Command" --> Interface
    Interface -- "7. Calls Method on View" --> View
    View -- "8. Executes Qt Logic" --> User
```

## 3. How the Information Flows

This architecture creates a clean, one-way flow of information for user actions.

**Example Flow: User clicks the 'Select Source' button.**

1.  **Raw UI Event**: The user clicks the `QPushButton` located inside the `LeftPanel`.

2.  **Event Bubbles Up**: The `LeftPanel` catches the raw `clicked` signal from the button. It knows this means the user wants to select a source, so it informs its parent, the main `UpdateDataView`.

3.  **API Signal Emitted**: The `UpdateDataView` receives the notification from its child panel. Its job is to translate this internal event into a formal API signal. It emits its own `source_select_requested` signal, which is part of the `IUpdateDataView` interface.

4.  **Presenter Reacts**: The `UpdateDataPresenter`, which is connected *only* to the `source_select_requested` signal on the interface, receives the notification. It has no idea that a `QPushButton` in a `LeftPanel` was the original source.

5.  **Presenter Issues Command**: The Presenter runs its business logic (e.g., determines the last used directory) and then issues a command back to the UI via the interface API, for example: `self.view.show_folder_dialog(...)`.

6.  **View Executes Command**: The `UpdateDataView` receives the `show_folder_dialog` call from the interface and executes the logic to display the actual Qt file dialog to the user.

### Key Insight

The Presenter remains completely ignorant of the UI's internal complexity. It doesn't know about the `LeftPanel` or `CenterPanel`. It only knows the API contract provided by `IUpdateDataView`. This enforces a clean separation of concerns and makes the entire system more robust, testable, and easier to maintain.
