"""
SelectGroupWidget - Schema-compliant composite widget for update_data module.

Combines OptionMenuWithLabel and SecondaryButton into a single configurable component
as defined in the MVP state schema. This widget is designed to match the schema
specifications exactly while maintaining compatibility with existing shared components.
"""

from dataclasses import dataclass
from typing import List, Optional
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout

from fm.gui._shared_components.widgets import (
    OptionMenuWithLabel,
    SecondaryButton
)


@dataclass
class SelectGroupConfig:
    """Configuration for SelectGroupWidget to match schema requirements."""
    label_text: str
    button_text: str = "Select..."
    menu_options: List[str] = None
    default_option: Optional[str] = None
    object_name: str = "select_group_widget"
    
    def __post_init__(self):
        """Initialize default menu options if not provided."""
        if self.menu_options is None:
            self.menu_options = []


class SelectGroupWidget(QWidget):
    """
    Schema-compliant composite widget combining OptionMenuWithLabel and SecondaryButton.
    
    This widget implements the SelectGroupWidget as defined in the MVP state schema,
    providing a unified interface for option selection with an action button.
    
    Signals:
        option_changed: Emitted when dropdown selection changes
        button_clicked: Emitted when the select button is clicked
        selection_changed: Alias for option_changed (schema compatibility)
    """
    
    # Primary signals matching schema specification
    option_changed = Signal(str)
    button_clicked = Signal()
    
    # Alias for schema compatibility
    selection_changed = Signal(str)
    
    def __init__(self, config: SelectGroupConfig, parent=None):
        """
        Initialize SelectGroupWidget with configuration.
        
        Args:
            config: SelectGroupConfig instance defining widget behavior
            parent: Parent widget
        """
        super().__init__(parent)
        self.config = config
        self._init_ui()
        self._connect_signals()
        self._apply_config()
    
    def _init_ui(self):
        """Initialize UI components."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)
        
        # Create option menu with label
        self.option_menu = OptionMenuWithLabel(
            label_text=self.config.label_text,
            options=self.config.menu_options
        )
        layout.addWidget(self.option_menu)
        
        # Create select button
        self.select_button = SecondaryButton(self.config.button_text)
        layout.addWidget(self.select_button)
        
        # Set object name for styling
        self.setObjectName(self.config.object_name)
    
    def _connect_signals(self):
        """Connect internal widget signals to external interface."""
        # Forward option menu signals
        self.option_menu.option_changed.connect(self.option_changed.emit)
        self.option_menu.option_changed.connect(self.selection_changed.emit)
        
        # Forward button signals
        self.select_button.clicked.connect(self.button_clicked.emit)
    
    def _apply_config(self):
        """Apply configuration settings."""
        if self.config.default_option:
            self.set_selected_option(self.config.default_option)
    
    # === PUBLIC API (Schema-compliant methods) ===
    
    def value(self) -> str:
        """Get currently selected option value."""
        return self.option_menu.combo_box.currentText()
    
    def setValue(self, text: str):
        """Set the selected option."""
        self.set_selected_option(text)
    
    def get_selected_option(self) -> str:
        """Get currently selected option (alternative method name)."""
        return self.value()
    
    def set_selected_option(self, option: str):
        """Set the selected option in the dropdown."""
        index = self.option_menu.combo_box.findText(option)
        if index >= 0:
            self.option_menu.combo_box.setCurrentIndex(index)
    
    def set_options(self, options: List[str]):
        """Update the dropdown options."""
        self.option_menu.combo_box.clear()
        self.option_menu.combo_box.addItems(options)
        self.config.menu_options = options
    
    def set_button_enabled(self, enabled: bool):
        """Enable or disable the select button."""
        self.select_button.setEnabled(enabled)
    
    def set_button_text(self, text: str):
        """Update the button text."""
        self.select_button.setText(text)
        self.config.button_text = text
    
    def get_button_text(self) -> str:
        """Get current button text."""
        return self.select_button.text()
    
    # === WIDGET STATE MANAGEMENT (Schema compliance) ===
    
    def set_visible(self, visible: bool):
        """Set widget visibility (schema state management)."""
        self.setVisible(visible)
    
    def set_enabled(self, enabled: bool):
        """Set widget enabled state (schema state management)."""
        self.setEnabled(enabled)
        
    def is_enabled(self) -> bool:
        """Check if widget is enabled."""
        return self.isEnabled()
    
    def is_visible(self) -> bool:
        """Check if widget is visible."""
        return self.isVisible()
    
    # === CONVENIENCE METHODS ===
    
    def get_current_selection(self) -> str:
        """Get current selection with error handling."""
        try:
            return self.value()
        except Exception:
            return ""
    
    def has_selection(self) -> bool:
        """Check if a valid selection is made."""
        return bool(self.get_current_selection().strip())
    
    def reset_to_default(self):
        """Reset to default option if configured."""
        if self.config.default_option:
            self.set_selected_option(self.config.default_option)
        elif self.config.menu_options:
            self.set_selected_option(self.config.menu_options[0])
    
    # === SCHEMA INTEGRATION HELPERS ===
    
    def get_state_info(self) -> dict:
        """Get current widget state for schema state engine."""
        return {
            'selected_option': self.get_selected_option(),
            'button_enabled': self.select_button.isEnabled(),
            'widget_visible': self.isVisible(),
            'widget_enabled': self.isEnabled(),
            'has_selection': self.has_selection()
        }
    
    def apply_state_config(self, state_config: dict):
        """Apply state configuration from schema state engine."""
        if 'visible' in state_config:
            self.set_visible(state_config['visible'])
        if 'enabled' in state_config:
            self.set_enabled(state_config['enabled'])
        if 'button_enabled' in state_config:
            self.set_button_enabled(state_config['button_enabled'])
        if 'selected_option' in state_config:
            self.set_selected_option(state_config['selected_option'])
        if 'button_text' in state_config:
            self.set_button_text(state_config['button_text'])


# === FACTORY FUNCTIONS FOR COMMON CONFIGURATIONS ===

def create_source_group_widget(parent=None) -> SelectGroupWidget:
    """Create SelectGroupWidget configured for source file selection."""
    # Use simple options for testing - can be replaced with actual enums later
    source_options = [
        "Select Folder",
        "Select Files",
        "Monitor Folder",
        "Import from Clipboard"
    ]

    config = SelectGroupConfig(
        label_text="1. Source Files",
        button_text="Select...",
        menu_options=source_options,
        default_option="Select Folder",
        object_name="source_group_widget"
    )
    return SelectGroupWidget(config, parent)


def create_save_group_widget(parent=None) -> SelectGroupWidget:
    """Create SelectGroupWidget configured for save location selection."""
    # Use simple options for testing - can be replaced with actual enums later
    save_options = [
        "Same as Source",
        "Select Different Folder",
        "Default Import Folder",
        "Ask Each Time"
    ]

    config = SelectGroupConfig(
        label_text="2. Save Location",
        button_text="Select...",
        menu_options=save_options,
        default_option="Same as Source",
        object_name="save_group_widget"
    )
    return SelectGroupWidget(config, parent)
