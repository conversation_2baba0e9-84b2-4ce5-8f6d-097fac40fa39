"""Center panel manager for the Update Data module."""

from fm.modules.update_data._view_components.center_panel_components import guide_pane
import pandas as pd
from fm.core.services.event_bus import Events, global_event_bus
from PySide6.QtCore import Signal
from PySide6.QtWidgets import Q<PERSON>oxLayout


from fm.gui._shared_components.base.base_panel_component import BasePanelComponent
from PySide6.QtWidgets import QWidget
# TODO: MOST OF THESE ARE UNUSED ? ! >> COMMENTED OUT BY DICKHEAD
from .center_panel_components._switcher import PanelSwitcher
# from .center_panel_components.data_pane import DataPane  # Missing - in archive
from .center_panel_components.file_pane import FilePane
# from .center_panel_components.welcome_pane import WelcomePane  # Missing
# from .center_panel_components.widgets.ud_status_bar import UpdateDataStatusBar  # Missing - in archive


class CenterPanelManager(BasePanelComponent):
    """Main center panel manager for the Update Data module.

    This class uses the Composite pattern to manage different panes
    that can be shown in the center area of the Update Data module.
    """

    # Signals for publishing events to subscribers
    publish_file_removed = Signal(str)  # Publishes path of removed file
    publish_file_selected = Signal(str)  # Publishes path of selected file

    # Pane identifiers
    WELCOME_PANE = "welcome"
    FILE_PANE = "file"
    DATA_PANE = "data"

    def __init__(self, parent=None):
        """Initialize the center panel manager."""
        super().__init__(parent)
        self.info_bar = None
        self.event_bus = global_event_bus
        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """Initialize the UI components."""
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

 
        self.pane_switcher = PanelSwitcher(self)
        self.main_layout.addWidget(self.pane_switcher)
        # TODO: self.status_bar = UpdateDataStatusBar() *SHOULD BE USING GUI.servies.info_bar
        #self.main_layout.addWidget(self.status_bar)
        self._create_panes()

     
        self.pane_switcher.show_component(self.WELCOME_PANE)

    def _create_panes(self):
        """Create and register all pane components."""
        # Guide pane replaces welcome pane functionality with state-driven approach
        self.guide_pane = guide_pane.GuidePaneWidget()
        self.pane_switcher.add_component(self.WELCOME_PANE, self.guide_pane)  # Fixed: use guide_pane instead of welcome_pane

        # File pane for file selection and management
        self.file_pane = FilePane()
        self.pane_switcher.add_component(self.FILE_PANE, self.file_pane)

        # Data pane deprecated - replaced by better table view in gui shared components
        # self.data_pane = DataPane()  # In archive - functionality moved to shared table_view
        # self.pane_switcher.add_component(self.DATA_PANE, self.data_pane)
      

    def _connect_signals(self):
        """Connect signals between components."""
        # todo does this match our new patter ? also, have we created our signal connector yet?
        self.file_pane.publish_file_removed.connect(self.publish_file_removed.emit)
        self.file_pane.publish_file_selected.connect(self.publish_file_selected.emit)
        

    def set_source_path(self, path: str):
        """Set the source folder path."""
        # Access the file pane directly
        self.file_pane.set_source_path(path)

    def set_save_path(self, path: str):
        """Set the save location path."""
        # Access the file pane directly
        self.file_pane.set_save_path(path)

    def set_files(self, files: list, source_dir: str = ""):
        """Set the files to display in the tree.

        Args:
            files: List of file paths
            source_dir: Source directory for relative paths
        """
        # Access the file pane directly
        self.file_pane.set_files(files, source_dir)
        # Show the file pane
        self.show_file_pane()

    def get_files(self) -> list[str]:
        """Get all file paths in the tree."""
        return self.file_pane.get_files()

    def display_master_csv(self, df: pd.DataFrame, file_info: str = ""):
        """Display a DataFrame in a table.

        Args:
            df: DataFrame to display
            file_info: Information about the file being displayed
        """
        # TODO: Data pane deprecated - need to implement with new shared table_view component
        # self.data_pane.display_dataframe(df, file_info)  # data_pane is in archive
        # self.show_data_pane()  # Method still exists but pane is missing

        # Temporary: Show in guide pane or implement new table view
        print(f"TODO: Display DataFrame with {len(df)} rows - {file_info}")
        # Consider using: from fm.gui._shared_components.widgets.table_view import TableView

    def display_welcome(self):
        """Display welcome message."""
        # Show the guide pane (replaces welcome pane functionality)
        self.show_welcome_pane()  # This now shows guide_pane

        # Reset guide pane to initial state
        self.guide_pane.reset_to_initial()

        # Clear file pane
        self.file_pane.clear()

    def show_error(self, message: str):
        """Show error message."""
        # Pass to current pane if it supports it
        current_component = self.pane_switcher.get_current_component()
        if current_component is not None:
            current_component.show_error(message)

        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, f"Error: {message}")

    def show_success(self, message: str):
        """Show success message."""
        # Pass to current pane if it supports it
        current_component = self.pane_switcher.get_current_component()
        if current_component is not None:
            current_component.show_success(message)

        # Also publish to event bus for InfoBar to pick up
        self.event_bus.publish(Events.INFO_MESSAGE, message)

    def show_welcome_pane(self):
        """Show the guide pane (replaces welcome pane)."""
        self.pane_switcher.show_component(self.WELCOME_PANE)  # Now contains guide_pane

    def show_file_pane(self):
        """Show the file display pane."""
        self.pane_switcher.show_component(self.FILE_PANE)

    def show_data_pane(self):
        """Show the data display pane."""
        # TODO: DATA_PANE is deprecated - implement with new table view component
        # self.pane_switcher.show_component(self.DATA_PANE)  # Pane doesn't exist
        print("TODO: Implement data pane with new shared table view component")

    def get_current_pane(self):
        """Get the currently displayed pane."""
        return self.pane_switcher.get_current_component_id()
